package com.springboot.pojo;

import lombok.Data;

@Data
public class DashboardInfo {
    /** 系统汇总信息 */
    private String summary;
    /** CPU使用率（如：15%） */
    private String cpuUsage;
    /** 内存使用（如：2048/4096MB） */
    private String memUsage;
    /** 磁盘使用（如：20G/100G） */
    private String diskUsage;
    /** 当前登录用户数 */
    private Integer userCount;
    /** 系统平均负载（如：0.12, 0.15, 0.10） */
    private String loadAverage;
}