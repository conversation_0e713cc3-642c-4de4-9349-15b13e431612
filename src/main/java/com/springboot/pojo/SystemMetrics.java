package com.springboot.pojo;

import lombok.Data;

@Data
public class SystemMetrics {
        double cpuUsage;       // CPU使用率（%）
        double memoryUsage;    // 内存使用率（%）
        double diskUsage;      // 磁盘使用率（%）
        double load1;          // 1分钟负载
        double load5;          // 5分钟负载
        double load15;         // 15分钟负载
        int userCount;         // 在线用户数
        int alerts;            // 告警数量
        int events;            // 事件数量
        String summary;        // 健康状态摘要
        String trend;          // 资源趋势
}