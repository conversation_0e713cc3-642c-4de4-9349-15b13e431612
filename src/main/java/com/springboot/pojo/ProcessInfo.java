package com.springboot.pojo;

public class ProcessInfo {
    private int pid;
    private String user;
    private String command;
    private double cpu;
    private double mem;
    private String startTime;
    private String status;

    public int getPid() { return pid; }
    public void setPid(int pid) { this.pid = pid; }
    public String getUser() { return user; }
    public void setUser(String user) { this.user = user; }
    public String getCommand() { return command; }
    public void setCommand(String command) { this.command = command; }
    public double getCpu() { return cpu; }
    public void setCpu(double cpu) { this.cpu = cpu; }
    public double getMem() { return mem; }
    public void setMem(double mem) { this.mem = mem; }
    public String getStartTime() { return startTime; }
    public void setStartTime(String startTime) { this.startTime = startTime; }
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
}