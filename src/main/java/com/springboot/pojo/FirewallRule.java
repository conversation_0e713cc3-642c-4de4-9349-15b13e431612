package com.springboot.pojo;

import lombok.Data;

/**
 * 防火墙规则实体类
 */
@Data
public class FirewallRule {
    /** 规则ID */
    private String id;
    /** 规则名称 */
    private String name;
    /** 协议类型（如TCP/UDP/ALL） */
    private String protocol;
    /** 端口号（单个或范围） */
    private String port;
    /** 来源IP（可为0.0.0.0/0） */
    private String sourceIp;
    /** 目标IP（可为本机或指定网段） */
    private String destIp;
    /** 动作（ACCEPT/DROP/REJECT） */
    private String action;
    /** 备注 */
    private String comment;
}