package com.springboot.utils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.concurrent.TimeUnit;

public class ShellUtil {
    /**
     * 执行Shell命令并返回输出结果。
     * @param cmd shell命令字符串
     * @return 命令输出内容或错误信息
     */
    public static String runShell(String cmd) {
        StringBuilder sb = new StringBuilder();
        try {
            // 检查命令是否为top并且没有-b参数，如果是则添加-b -n 1参数
            if (cmd.startsWith("top") && !cmd.contains("-b")) {
                cmd = "top -b -n 1 " + cmd.substring(3);
            }

            String[] command = {"/bin/sh", "-c", cmd};
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.redirectErrorStream(true); // 将错误流重定向到输出流

            Process proc = processBuilder.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(proc.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line).append("\n");
            }
            reader.close();

            // 等待5秒或直到进程结束
            proc.waitFor(5, TimeUnit.SECONDS);

            // 检查进程是否仍在运行，如果是，则强制终止
            if (proc.isAlive()) {
                proc.destroyForcibly();
            }

            int exitCode = proc.exitValue();
            if (exitCode != 0) {
                sb.append("Command execution failed with exit code: ").append(exitCode);
            }

        } catch (Exception e) {
            sb.append("error: ").append(e.getMessage());
        }
        return sb.toString();
    }
}