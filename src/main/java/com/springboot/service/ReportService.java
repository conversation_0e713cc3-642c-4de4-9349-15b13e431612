package com.springboot.service;

import com.springboot.pojo.ReportInfo;
import java.util.List;

public interface ReportService {
    /**
     * 获取报告列表
     */
    List<ReportInfo> listReports(String startTime, String endTime);

    /**
     * 根据ID获取报告
     */
    ReportInfo getReportById(String reportId);

    /**
     * 生成新报告
     */
    ReportInfo generateReport();

    /**
     * 获取报告PDF内容
     */
    byte[] getReportPdf(String reportId);

    /**
     * 检查PDF是否可用
     */
    boolean isPdfAvailable(String reportId);
}