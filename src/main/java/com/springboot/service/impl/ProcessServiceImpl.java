package com.springboot.service.impl;

import com.springboot.pojo.ProcessInfo;
import com.springboot.service.ProcessService;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

@Service
public class ProcessServiceImpl implements ProcessService {
    @Override
    public List<ProcessInfo> listProcesses() {
        List<ProcessInfo> list = new ArrayList<>();
        try {
            Process proc = Runtime.getRuntime().exec("ps -eo pid,user,comm,%cpu,%mem,lstart,state");
            BufferedReader reader = new BufferedReader(new InputStreamReader(proc.getInputStream()));
            String line;
            boolean first = true;
            while ((line = reader.readLine()) != null) {
                if (first) { first = false; continue; }
                String[] arr = line.trim().split("\\s+", 8); // 使用正则表达式分割，最多8个字段
                if (arr.length < 8) continue; // 如果字段不足8个，跳过

                ProcessInfo info = new ProcessInfo();
                // 解析 PID
                try {
                    info.setPid(Integer.parseInt(arr[0]));
                } catch (NumberFormatException e) {
                    System.err.println("Invalid PID format: " + arr[0]);
                    continue; // 跳过当前行
                }

                // 解析 User
                info.setUser(arr[1]);

                // 解析 Command
                info.setCommand(arr[2]);

                // 解析 %CPU
                try {
                    // 尝试将 %cpu 字段转换为数字，过滤掉非数字字符
                    String cpuStr = arr[3].replaceAll("[^-\\.0-9]", "");
                    info.setCpu(cpuStr.isEmpty() ? 0.0 : Double.parseDouble(cpuStr));
                } catch (NumberFormatException e) {
                    System.err.println("Invalid %CPU format: " + arr[3]);
                    info.setCpu(0.0); // 设置默认值
                }

                // 解析 %MEM
                try {
                    // 尝试将 %mem 字段转换为数字，过滤掉非数字字符
                    String memStr = arr[4].replaceAll("[^-\\.0-9]", "");
                    info.setMem(memStr.isEmpty() ? 0.0 : Double.parseDouble(memStr));
                } catch (NumberFormatException e) {
                    System.err.println("Invalid %MEM format: " + arr[4]);
                    info.setMem(0.0); // 设置默认值
                }

                // 解析 Start Time
                if (arr.length >= 7) {
                    info.setStartTime(arr[5] + " " + arr[6] + " " + arr[7]);
                } else {
                    info.setStartTime("");
                }

                // 解析 Status
                info.setStatus(arr.length > 7 ? arr[7] : "");

                list.add(info);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public boolean killProcess(int pid) {
        try {
            Process proc = Runtime.getRuntime().exec("kill -9 " + pid);
            proc.waitFor();
            return proc.exitValue() == 0;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean startProcess(String command) {
        try {
            Process proc = Runtime.getRuntime().exec(command);
            return proc.isAlive();
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean restartProcess(int pid, String command) {
        return killProcess(pid) && startProcess(command);
    }
}