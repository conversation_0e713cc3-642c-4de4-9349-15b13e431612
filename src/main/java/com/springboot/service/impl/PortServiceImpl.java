package com.springboot.service.impl;

import com.springboot.pojo.PortCheckResult;
import com.springboot.pojo.PortScanResult;
import com.springboot.service.PortService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class PortServiceImpl implements PortService {

    @Override
    public PortCheckResult checkPort(String ip, int port) {
        PortCheckResult result = new PortCheckResult();
        result.setIp(ip);
        result.setPort(port);
        try {
            // 查询本机端口是否被占用及进程信息
            Process process = Runtime.getRuntime().exec("netstat -tunlp | grep :" + port);
            java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains("LISTEN")) {
                    String[] parts = line.trim().split("\\s+");
                    if (parts.length > 6) { // 确保有足够的部分
                        // 提取进程信息，格式通常为 PID/程序名
                        String pidInfo = parts[6];
                        if (pidInfo.contains("/")) {
                            String pid = pidInfo.split("/")[0]; // 提取PID
                            result.setProcessInfo("PID: " + pid);
                        } else {
                            result.setProcessInfo("无进程信息");
                        }
                    } else {
                        result.setProcessInfo("无进程信息");
                    }
                    result.setStatus("open");
                    return result;
                }
            }
            result.setStatus("closed");
            result.setProcessInfo("无进程");
        } catch (Exception e) {
            result.setStatus("error");
            result.setProcessInfo("检测异常: " + e.getMessage());
        }
        return result;
    }

    @Override
    public List<PortScanResult> scanPorts(String ipOrCidr) {
        List<PortScanResult> results = new ArrayList<>();
        try {
            // 查询本机所有监听的 TCP 和 UDP 端口及进程
            Process process = Runtime.getRuntime().exec("netstat -tunlp");
            java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.InputStreamReader(process.getInputStream()));
            String line;
            Pattern pattern = Pattern.compile(".*:(\\d+)\\s+.*");
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("Active")) continue; // 跳过标题行和空行

                Matcher matcher = pattern.matcher(line);
                if (matcher.find()) {
                    String portStr = matcher.group(1);
                    int port = Integer.parseInt(portStr);
                    String[] parts = line.split("\\s+");
                    String pid = parts.length > 6 ? parts[6].split("/")[0] : "未知"; // PID通常在第7个位置

                    PortScanResult r = new PortScanResult();
                    r.setIp("127.0.0.1");
                    r.setPort(port);
                    r.setStatus("open");
                    r.setProcessInfo("PID: " + pid);
                    results.add(r);
                }
            }
        } catch (Exception e) {
            // 异常时返回空列表
            System.err.println("扫描异常: " + e.getMessage());
        }
        return results;
    }
}