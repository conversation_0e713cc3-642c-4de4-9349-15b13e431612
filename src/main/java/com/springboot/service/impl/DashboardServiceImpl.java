package com.springboot.service.impl;

import com.springboot.pojo.DashboardInfo;
import com.springboot.service.DashboardService;
import com.springboot.utils.ShellUtil;
import org.springframework.stereotype.Service;
import java.io.BufferedReader;
import java.io.InputStreamReader;

@Service
public class DashboardServiceImpl implements DashboardService {
    /**
     * 获取运维大盘信息，包括系统汇总、CPU、内存、磁盘、用户数、负载等
     */
    @Override
    public DashboardInfo getDashboardInfo() {
        DashboardInfo info = new DashboardInfo();
        // 系统汇总信息
        String summary = ShellUtil.runShell("uptime && who | wc -l && tail -n 10 /var/log/messages");
        info.setSummary(summary);
        // CPU使用率
        String cpu = ShellUtil.runShell("top -bn1 | grep 'Cpu(s)' | awk '{print $2 + $4}'");
        info.setCpuUsage(cpu != null ? cpu.trim() + "%" : "-");
        // 内存使用
        String mem = ShellUtil.runShell("free -m | grep 'Mem' | awk '{print $3 \"/\" $2 \"MB\"}'");
        info.setMemUsage(mem != null ? mem.trim() : "-");
        // 磁盘使用
        String disk = ShellUtil.runShell("df -h | grep '^/dev/' | awk '{used+=$3; size+=$2} END {print used \"/\" size \"G\"}'");
        info.setDiskUsage(disk != null ? disk.trim() : "-");
        // 当前登录用户数
        String userCount = ShellUtil.runShell("who | wc -l");
        try {
            info.setUserCount(userCount != null ? Integer.parseInt(userCount.trim()) : 0);
        } catch (Exception e) {
            info.setUserCount(0);
        }
        // 系统平均负载
        String load = ShellUtil.runShell("uptime | awk -F'load average:' '{print $2}'");
        info.setLoadAverage(load != null ? load.trim() : "-");
        return info;
    }

    // 保留原有通用shell方法
    private String runShell(String cmd) {
        StringBuilder sb = new StringBuilder();
        try {
            String[] command = {"/bin/sh", "-c", cmd};
            Process proc = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(proc.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line).append("\n");
            }
            reader.close();
            proc.waitFor();
        } catch (Exception e) {
            sb.append("error: ").append(e.getMessage());
        }
        return sb.toString();
    }
}