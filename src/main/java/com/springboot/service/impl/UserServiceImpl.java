package com.springboot.service.impl;

import com.springboot.pojo.UserInfo;
import com.springboot.service.UserService;
import com.springboot.utils.ShellUtil;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;

/**
 * Linux服务器用户管理服务实现
 */
@Service
public class UserServiceImpl implements UserService {
    /**
     * 查询所有用户
     */
    @Override
    public List<UserInfo> listUsers() {
        List<UserInfo> users = new ArrayList<>();
        String result = ShellUtil.runShell("cat /etc/passwd");
        String[] lines = result.split("\n");
        for(String line : lines) {
            String[] arr = line.split(":");
            if(arr.length >= 7) {
                UserInfo user = new UserInfo();
                user.setUsername(arr[0]);
                user.setUid(arr[2]);
                user.setGid(arr[3]);
                user.setComment(arr[4]);
                user.setHome(arr[5]);
                user.setShell(arr[6]);
                users.add(user);
            }
        }
        return users;
    }
}