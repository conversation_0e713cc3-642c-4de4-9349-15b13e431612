package com.springboot.service.impl;

import com.springboot.pojo.LogInfo;
import com.springboot.service.LogService;
import com.springboot.utils.ShellUtil;
import org.springframework.stereotype.Service;

@Service
public class LogServiceImpl implements LogService {
    @Override
    public LogInfo getLogInfo() {
        LogInfo info = new LogInfo();
        info.setLogs(ShellUtil.runShell("tail -n 100 /var/log/messages"));
        return info;
    }
}