package com.springboot.service.impl;

import com.springboot.pojo.AssetInfo;
import com.springboot.service.AssetService;
import com.springboot.utils.ShellUtil;
import org.springframework.stereotype.Service;

@Service
public class AssetServiceImpl implements AssetService {
    @Override
    public AssetInfo getAssetInfo() {
        AssetInfo info = new AssetInfo();
        // 通过命令获取主机名和IP等资产信息
        String assets = ShellUtil.runShell("hostname && hostname -I && lscpu | grep 'Model name' && free -m | grep 'Mem' && df -h | grep '^/dev/'");
        info.setAssets(assets);
        return info;
    }
}