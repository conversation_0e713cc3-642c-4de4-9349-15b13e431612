package com.springboot.service.impl;

import com.springboot.pojo.*;
import com.springboot.service.SystemMonitorService;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class SystemMonitorServiceImpl implements SystemMonitorService {

    @Override
    public SystemInfo getSystemInfo() {
        try {
            Process process = Runtime.getRuntime().exec("top -bn1");
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            
            String line;
            SystemInfo systemInfo = new SystemInfo();
            
            // 读取系统信息行
            if ((line = reader.readLine()) != null) {
                parseSystemLine(line, systemInfo);
            }
            
            // 读取任务信息行
            if ((line = reader.readLine()) != null) {
                parseTasksLine(line, systemInfo);
            }
            
            // 读取CPU信息行
            if ((line = reader.readLine()) != null) {
                parseCpuLine(line, systemInfo);
            }
            
            // 读取内存信息行
            if ((line = reader.readLine()) != null) {
                parseMemoryLine(line, systemInfo);
            }
            
            // 读取交换空间信息行
            if ((line = reader.readLine()) != null) {
                parseSwapLine(line, systemInfo);
            }
            
            return systemInfo;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public CpuInfo getCpuInfo() {
        SystemInfo systemInfo = getSystemInfo();
        return systemInfo != null ? systemInfo.getCpuInfo() : null;
    }

    @Override
    public MemoryInfo getMemoryInfo() {
        SystemInfo systemInfo = getSystemInfo();
        return systemInfo != null ? systemInfo.getMemoryInfo() : null;
    }

    @Override
    public ProcessInfoVo[] getTopProcesses(int count) {
        try {
            Process process = Runtime.getRuntime().exec("top -bn1");
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            
            // 跳过标题行
            for (int i = 0; i < 7; i++) {
                reader.readLine();
            }
            
            List<ProcessInfoVo> processes = new ArrayList<>();
            String line;
            
            // 读取进程信息
            while ((line = reader.readLine()) != null && !line.trim().isEmpty()) {
                ProcessInfoVo processInfo = parseProcessLine(line);
                if (processInfo != null) {
                    processes.add(processInfo);
                }
            }
            
            // 返回前N个进程
            return processes.stream()
                    .sorted((p1, p2) -> Double.compare(p2.getCpuUsage(), p1.getCpuUsage()))
                    .limit(count)
                    .toArray(ProcessInfoVo[]::new);
        } catch (IOException e) {
            e.printStackTrace();
            return new ProcessInfoVo[0];
        }
    }

    private void parseSystemLine(String line, SystemInfo systemInfo) {
        // 解析 top - 15:26:29 up  1:20,  2 users,  load average: 0.49, 0.45, 0.30
        Pattern pattern = Pattern.compile("top - (.*) up (.*), +(\\d+) users, +load average: (.*), (.*), (.*)");
        Matcher matcher = pattern.matcher(line);
        
        if (matcher.find()) {
            systemInfo.setCurrentTime(matcher.group(1));
            systemInfo.setUptime(matcher.group(2));
            systemInfo.setUserCount(Integer.parseInt(matcher.group(3)));
            systemInfo.setLoadAverage1(Double.parseDouble(matcher.group(4)));
            systemInfo.setLoadAverage5(Double.parseDouble(matcher.group(5)));
            systemInfo.setLoadAverage15(Double.parseDouble(matcher.group(6)));
        }
    }

    private void parseTasksLine(String line, SystemInfo systemInfo) {
        // 解析 Tasks: 282 total,   1 running, 281 sleeping,   0 stopped,   0 zombie
        Pattern pattern = Pattern.compile("Tasks: (\\d+) total, +(\\d+) running, +(\\d+) sleeping, +(\\d+) stopped, +(\\d+) zombie");
        Matcher matcher = pattern.matcher(line);
        
        if (matcher.find()) {
            systemInfo.setTotalTasks(Integer.parseInt(matcher.group(1)));
            systemInfo.setRunningTasks(Integer.parseInt(matcher.group(2)));
            systemInfo.setSleepingTasks(Integer.parseInt(matcher.group(3)));
            systemInfo.setStoppedTasks(Integer.parseInt(matcher.group(4)));
            systemInfo.setZombieTasks(Integer.parseInt(matcher.group(5)));
        }
    }

    private void parseCpuLine(String line, SystemInfo systemInfo) {
        // 解析 %Cpu(s):  0.0 us,  0.3 sy,  0.0 ni, 99.5 id,  0.0 wa,  0.1 hi,  0.0 si,  0.0 st
        Pattern pattern = Pattern.compile("%Cpu\\(s\\): +([\\d.]+) us, +([\\d.]+) sy, +([\\d.]+) ni, +([\\d.]+) id, +([\\d.]+) wa, +([\\d.]+) hi, +([\\d.]+) si, +([\\d.]+) st");
        Matcher matcher = pattern.matcher(line);
        
        if (matcher.find()) {
            CpuInfo cpuInfo = new CpuInfo();
            cpuInfo.setUser(Double.parseDouble(matcher.group(1)));
            cpuInfo.setSystem(Double.parseDouble(matcher.group(2)));
            cpuInfo.setNice(Double.parseDouble(matcher.group(3)));
            cpuInfo.setIdle(Double.parseDouble(matcher.group(4)));
            cpuInfo.setWait(Double.parseDouble(matcher.group(5)));
            cpuInfo.setHardwareIrq(Double.parseDouble(matcher.group(6)));
            cpuInfo.setSoftwareIrq(Double.parseDouble(matcher.group(7)));
            cpuInfo.setSteal(Double.parseDouble(matcher.group(8)));
            systemInfo.setCpuInfo(cpuInfo);
        }
    }

    private void parseMemoryLine(String line, SystemInfo systemInfo) {
        // 解析 MiB Mem :   6645.1 total,    320.6 free,   4196.2 used,   2128.3 buff/cache
        Pattern pattern = Pattern.compile("MiB Mem +: +([\\d.]+) total, +([\\d.]+) free, +([\\d.]+) used, +([\\d.]+) buff/cache");
        Matcher matcher = pattern.matcher(line);
        
        if (matcher.find()) {
            MemoryInfo memoryInfo = new MemoryInfo();
            memoryInfo.setTotal(Double.parseDouble(matcher.group(1)));
            memoryInfo.setFree(Double.parseDouble(matcher.group(2)));
            memoryInfo.setUsed(Double.parseDouble(matcher.group(3)));
            memoryInfo.setBuffered(Double.parseDouble(matcher.group(4)));
            systemInfo.setMemoryInfo(memoryInfo);
        }
    }

    private void parseSwapLine(String line, SystemInfo systemInfo) {
        // 解析 MiB Swap:   4032.0 total,   3993.6 free,     38.4 used.   2121.5 avail Mem
        Pattern pattern = Pattern.compile("MiB Swap: +([\\d.]+) total, +([\\d.]+) free, +([\\d.]+) used. +([\\d.]+) avail Mem");
        Matcher matcher = pattern.matcher(line);
        
        if (matcher.find()) {
            MemoryInfo memoryInfo = systemInfo.getMemoryInfo();
            if (memoryInfo != null) {
                memoryInfo.setSwapTotal(Double.parseDouble(matcher.group(1)));
                memoryInfo.setSwapFree(Double.parseDouble(matcher.group(2)));
                memoryInfo.setSwapUsed(Double.parseDouble(matcher.group(3)));
                memoryInfo.setAvailable(Double.parseDouble(matcher.group(4)));
            }
        }
    }

    private ProcessInfoVo parseProcessLine(String line) {
        // 解析进程信息行
        Pattern pattern = Pattern.compile("\\s*(\\d+)\\s+(\\S+)\\s+(\\d+)\\s+(\\S+)\\s+(\\S+)\\s+(\\S+)\\s+(\\S+)\\s+(\\S)\\s+([\\d.]+)\\s+([\\d.]+)\\s+(\\S+)\\s+(.*)");
        Matcher matcher = pattern.matcher(line);
        
        if (matcher.find()) {
            ProcessInfoVo processInfo = new ProcessInfoVo();
            processInfo.setPid(Integer.parseInt(matcher.group(1)));
            processInfo.setUser(matcher.group(2));
            processInfo.setPriority(Integer.parseInt(matcher.group(3)));
            processInfo.setNice(matcher.group(4));
            processInfo.setVirtualMemory(matcher.group(5));
            processInfo.setResidentMemory(matcher.group(6));
            processInfo.setSharedMemory(matcher.group(7));
            processInfo.setStatus(matcher.group(8));
            processInfo.setCpuUsage(Double.parseDouble(matcher.group(9)));
            processInfo.setMemoryUsage(Double.parseDouble(matcher.group(10)));
            processInfo.setCpuTime(matcher.group(11));
            processInfo.setCommand(matcher.group(12));
            return processInfo;
        }
        return null;
    }
}    