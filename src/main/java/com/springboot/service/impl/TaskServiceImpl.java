package com.springboot.service.impl;

import com.springboot.pojo.TaskInfo;
import com.springboot.service.TaskService;
import com.springboot.utils.ShellUtil;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class TaskServiceImpl implements TaskService {

    // 使用内存存储任务信息，实际项目中可以替换为数据库
    private final ConcurrentHashMap<String, TaskInfo> taskMap = new ConcurrentHashMap<>();

    // Quartz调度器
    private final Scheduler scheduler;

    public TaskServiceImpl() throws SchedulerException {
        // 初始化Quartz调度器
        scheduler = StdSchedulerFactory.getDefaultScheduler();
        scheduler.start();
    }

    @Override
    public List<TaskInfo> listTasks() {
        // 将内存中的任务信息转换为TaskInfo列表返回
        return new ArrayList<>(taskMap.values());
    }

    @Override
    public boolean addTask(TaskInfo task) {
        try {
            // 设置任务的创建时间和更新时间
            task.setId(UUID.randomUUID().toString());
            task.setCreateTime(LocalDateTime.now());

            // 添加到内存中
            taskMap.put(task.getId(), task);

            // 创建Quartz任务
            JobDetail jobDetail = JobBuilder.newJob(SimpleJob.class)
                    .withIdentity(task.getId(), "group1")
                    .build();
            jobDetail.getJobDataMap().put("command", task.getCommand());

            // 创建触发器
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(task.getCron());
            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(task.getId(), "group1")
                    .forJob(jobDetail)
                    .withSchedule(scheduleBuilder)
                    .build();

            // 调度任务
            scheduler.scheduleJob(jobDetail, trigger);

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean deleteTask(String id) {
        try {
            // 检查任务是否存在
            if (!taskMap.containsKey(id)) {
                return false;
            }

            // 删除任务
            scheduler.pauseJob(JobKey.jobKey(id, "group1"));
            scheduler.deleteJob(JobKey.jobKey(id, "group1"));
            taskMap.remove(id);

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    // 定义任务执行的内容
    public static class SimpleJob implements Job {
        @Override
        public void execute(JobExecutionContext context) throws JobExecutionException {
            JobDataMap dataMap = context.getJobDetail().getJobDataMap();
            String command = dataMap.getString("command");
            System.out.println("执行命令: " + command);
            ShellUtil.runShell(command);
        }
    }
}