package com.springboot.service.impl;

import com.springboot.pojo.FirewallRule;
import com.springboot.service.FirewallService;
import com.springboot.utils.ShellUtil;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;

/**
 * 防火墙服务实现类，通过调用脚本实现规则管理
 */
@Service
public class FirewallServiceImpl implements FirewallService {
    /**
     * 查询所有防火墙规则（通过iptables命令获取）
     */
    @Override
    public List<FirewallRule> listRules() {
        List<FirewallRule> rules = new ArrayList<>();
        try {
            String result = ShellUtil.runShell("iptables -S");
            if (result != null && !result.isEmpty()) {
                String[] lines = result.split("\n");
                for (String line : lines) {
                    // 这里只做简单解析，实际生产环境需更复杂的正则和字段映射
                    if (line.startsWith("-A")) {
                        String[] arr = line.split(" ");
                        FirewallRule rule = new FirewallRule();
                        rule.setId(""); // iptables无ID，可用规则字符串hash
                        rule.setName("");
                        rule.setProtocol("");
                        rule.setPort("");
                        rule.setSourceIp("");
                        rule.setDestIp("");
                        rule.setAction("");
                        rule.setComment(line);
                        rules.add(rule);
                    }
                }
            }
        } catch (Exception e) {
            // 异常时返回空列表
        }
        return rules;
    }

    /**
     * 添加防火墙规则（直接调用iptables命令）
     */
    @Override
    public boolean addRule(FirewallRule rule) {
        try {
            String cmd = String.format("iptables -A INPUT -p %s --dport %s -s %s -d %s -j %s", rule.getProtocol(), rule.getPort(), rule.getSourceIp(), rule.getDestIp(), rule.getAction());
            String result = ShellUtil.runShell(cmd);
            return result == null || result.isEmpty();
        } catch (Exception e) {
            return false;
        }
    }


}