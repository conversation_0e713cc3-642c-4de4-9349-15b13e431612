package com.springboot.service.impl;

import com.springboot.pojo.MonitorInfo;
import com.springboot.service.MonitorService;
import org.springframework.stereotype.Service;

import com.springboot.utils.ShellUtil;

@Service
public class MonitorServiceImpl implements MonitorService {
    @Override
    public MonitorInfo getMonitorInfo() {
        MonitorInfo info = new MonitorInfo();
        String cpuRaw = ShellUtil.runShell("top -bn1 | grep 'Cpu(s)' | awk '{print $2 + $4}'");
        String memRaw = ShellUtil.runShell("free -m | grep 'Mem'");
        String diskRaw = ShellUtil.runShell("df -h | grep '^/dev/'");
        info.setCpuUsage(cpuRaw.trim());
        info.setMemUsage(memRaw.trim());
        info.setDiskUsage(diskRaw.trim());
        // 简单告警逻辑
        String alarm = "";
        try {
            double cpu = Double.parseDouble(cpuRaw.replaceAll("[^\\d.]", ""));
            if(cpu > 80) alarm += "CPU使用率过高("+cpu+"%) ";
        } catch(Exception e) {}
        try {
            String[] memArr = memRaw.split("\\s+");
            if(memArr.length >= 3) {
                int used = Integer.parseInt(memArr[2]);
                int total = Integer.parseInt(memArr[1]);
                if(total > 0 && used * 100.0 / total > 80) alarm += "内存使用率过高("+(used*100/total)+"%) ";
            }
        } catch(Exception e) {}
        // 可扩展磁盘等其他告警
        info.setAlarm(alarm.isEmpty()?"正常":alarm.trim());
        // 检查常用服务状态
        StringBuilder serviceStatus = new StringBuilder();
        String[] services = {"sshd", "nginx", "mysqld"};
        for(String svc : services) {
            String status = ShellUtil.runShell("systemctl is-active "+svc).trim();
            serviceStatus.append(svc).append(": ").append(status).append("; ");
        }
        info.setServiceStatus(serviceStatus.toString().trim());
        return info;
    }
}