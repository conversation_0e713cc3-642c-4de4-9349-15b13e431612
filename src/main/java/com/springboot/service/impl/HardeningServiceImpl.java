package com.springboot.service.impl;

import com.springboot.pojo.HardeningResult;
import com.springboot.service.HardeningService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class HardeningServiceImpl implements HardeningService {
    /**
     * 检查root用户是否存在弱口令（仅检测是否设置了空密码或密码过于简单，实际生产环境建议更复杂检测）
     */
    @Override
    public List<HardeningResult> checkWeakPassword() {
        List<HardeningResult> list = new ArrayList<>();
        HardeningResult r = new HardeningResult();
        r.setItem("root用户弱口令");
        String result = com.springboot.utils.ShellUtil.runShell("sudo grep '^root:' /etc/shadow | awk -F: '{print $2}'");
        if (result.trim().isEmpty() || result.contains("*")) {
            r.setStatus("安全");
            r.setDetail("root用户未设置密码或已锁定");
        } else if (result.length() < 20) {
            r.setStatus("存在风险");
            r.setDetail("root密码可能过于简单，请检查密码复杂度");
        } else {
            r.setStatus("安全");
            r.setDetail("root密码已设置");
        }
        r.setSuggestion("建议定期修改root密码，设置高强度密码");
        list.add(r);
        return list;
    }

    /**
     * 检查系统关键补丁是否已安装（以yum/apt为例，实际可根据发行版调整）
     */
    @Override
    public List<HardeningResult> checkSystemPatch() {
        List<HardeningResult> list = new ArrayList<>();
        HardeningResult r = new HardeningResult();
        r.setItem("系统补丁");
        String result = com.springboot.utils.ShellUtil.runShell("(which yum && yum check-update) || (which apt && apt list --upgradable)");
        if (result.contains("No packages marked for update") || result.contains("已安装最新版本")) {
            r.setStatus("已安装");
            r.setDetail("所有关键补丁已安装");
        } else if (result.trim().isEmpty()) {
            r.setStatus("未知");
            r.setDetail("无法检测补丁状态");
        } else {
            r.setStatus("存在可更新补丁");
            r.setDetail(result.length() > 200 ? result.substring(0,200)+"..." : result);
        }
        r.setSuggestion("保持系统及时更新");
        list.add(r);
        return list;
    }

    /**
     * 检查系统开放端口情况
     */
    @Override
    public List<HardeningResult> checkOpenPorts() {
        List<HardeningResult> list = new ArrayList<>();
        HardeningResult r = new HardeningResult();
        r.setItem("端口暴露");
        String result = com.springboot.utils.ShellUtil.runShell("ss -tuln | grep LISTEN");
        if (result.trim().isEmpty()) {
            r.setStatus("安全");
            r.setDetail("未检测到开放端口");
        } else {
            r.setStatus("部分暴露");
            r.setDetail(result.length() > 200 ? result.substring(0,200)+"..." : result);
        }
        r.setSuggestion("关闭不必要端口");
        list.add(r);
        return list;
    }

    /**
     * 检查SSH配置（如是否允许root远程登录）
     */
    @Override
    public List<HardeningResult> checkOtherItems() {
        List<HardeningResult> list = new ArrayList<>();
        HardeningResult r = new HardeningResult();
        r.setItem("SSH配置");
        String result = com.springboot.utils.ShellUtil.runShell("grep -i '^PermitRootLogin' /etc/ssh/sshd_config");
        if (result.toLowerCase().contains("no")) {
            r.setStatus("安全");
            r.setDetail("已禁止root远程登录");
        } else {
            r.setStatus("存在风险");
            r.setDetail("建议禁止root远程登录，当前配置：" + result.trim());
        }
        r.setSuggestion("建议在/etc/ssh/sshd_config中设置PermitRootLogin no");
        list.add(r);
        return list;
    }
}