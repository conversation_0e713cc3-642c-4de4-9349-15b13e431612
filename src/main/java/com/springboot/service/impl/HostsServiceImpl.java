package com.springboot.service.impl;

import com.springboot.pojo.HostsEntry;
import com.springboot.service.HostsService;
import org.springframework.stereotype.Service;
import java.io.*;
import java.util.*;

@Service
public class HostsServiceImpl implements HostsService {

    @Override
    public List<HostsEntry> listHosts() {
        List<HostsEntry> hostsList = new ArrayList<>();
        try {
            File hostsFile = new File("/etc/hosts");
            BufferedReader reader = new BufferedReader(new FileReader(hostsFile));
            String line;
            while ((line = reader.readLine()) != null) {
                String[] parts = line.split("\\s+", 2);
                if (parts.length == 2) {
                    hostsList.add(new HostsEntry(parts[0], parts[1]));
                }
            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return hostsList;
    }

    @Override
    public void addHost(HostsEntry entry) {
        try {
            FileWriter writer = new FileWriter("/etc/hosts", true);
            writer.write(entry.getIp() + " " + entry.getHostname() + System.lineSeparator());
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Override
    public void deleteHost(HostsEntry entry) {
        try (BufferedReader reader = new BufferedReader(new FileReader("/etc/hosts"))) {
            List<String> lines = new ArrayList<>();
            boolean found = false;
            String line;

            while ((line = reader.readLine()) != null) {
                // 按任意空白字符分割，并限制分割次数为 2
                String[] parts = line.trim().split("\\s+", 2);
                // 检查是否包含有效的 IP 和主机名
                if (parts.length == 2) {
                    String ip = parts[0].trim(); // 去除 IP 前后多余空格
                    String hostname = parts[1].trim(); // 去除主机名前后多余空格
                    if (ip.equals(entry.getIp()) && hostname.equals(entry.getHostname())) {
                        found = true;
                        continue; // 跳过添加到 lines 列表
                    }
                }

                // 将当前行添加到 lines 列表
                lines.add(line);
            }
            if (found) {
                try (FileWriter writer = new FileWriter("/etc/hosts")) {
                    for (String content : lines) {
                        writer.write(content + System.lineSeparator());
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}