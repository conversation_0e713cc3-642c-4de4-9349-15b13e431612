package com.springboot.service.impl;

import com.springboot.pojo.ServiceInfo;
import com.springboot.service.ServiceService;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * 服务管理实现类，提供服务的查询与操作方法
 */
@Service
public class ServiceServiceImpl implements ServiceService {
    @Override
    public List<ServiceInfo> listServices() {
        List<ServiceInfo> list = new ArrayList<>();
        try {
            Process proc = Runtime.getRuntime().exec("systemctl list-units --type=service --all --no-pager --no-legend");
            BufferedReader reader = new BufferedReader(new InputStreamReader(proc.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                String[] arr = line.trim().split("\\s+", 5);
                if (arr.length >= 4) {
                    ServiceInfo info = new ServiceInfo();
                    info.setName(arr[0].replace(".service", ""));
                    info.setStatus(arr[3].equals("running") ? "运行中" : "已停止");
                    info.setDescription(arr.length == 5 ? arr[4] : "");

                    // 查询PID
                    Process pidProc = Runtime.getRuntime().exec("systemctl show -p MainPID " + arr[0]);
                    BufferedReader pidReader = new BufferedReader(new InputStreamReader(pidProc.getInputStream()));
                    String pidLine = pidReader.readLine();
                    String pid = "-";
                    if (pidLine != null && pidLine.contains("MainPID=")) {
                        pid = pidLine.split("=", 2)[1].trim();
                    }
                    info.setPid(pid);

                    // 查询启动类型
                    Process startTypeProc = Runtime.getRuntime().exec("systemctl is-enabled " + arr[0]);
                    BufferedReader startTypeReader = new BufferedReader(new InputStreamReader(startTypeProc.getInputStream()));
                    String startTypeLine = startTypeReader.readLine();
                    String startType = "手动";
                    if (startTypeLine != null && startTypeLine.trim().equals("enabled")) {
                        startType = "自动";
                    }
                    info.setStartType(startType);

                    list.add(info);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return list;
    }

    @Override
    public boolean startService(String name) {
        return execCmd("systemctl start " + name);
    }

    @Override
    public boolean stopService(String name) {
        return execCmd("systemctl stop " + name);
    }

    @Override
    public boolean restartService(String name) {
        return execCmd("systemctl restart " + name);
    }

    /**
     * 执行shell命令
     */
    private boolean execCmd(String cmd) {
        try {
            Process proc = Runtime.getRuntime().exec(cmd);
            int code = proc.waitFor();
            return code == 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}