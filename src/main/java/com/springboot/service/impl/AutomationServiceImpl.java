package com.springboot.service.impl;

import com.springboot.pojo.AutomationResult;
import com.springboot.service.AutomationService;
import com.springboot.utils.ShellUtil;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 自动化运维服务实现
 */
@Service
public class AutomationServiceImpl implements AutomationService {
    @Override
    public List<AutomationResult> uploadAndExecute(MultipartFile script) {
        List<AutomationResult> results = new ArrayList<>();
        String scriptName = script.getOriginalFilename();
        String host = "localhost";
        try {
            String content = new String(script.getBytes());
            String[] commands = content.split("\\r?\\n");
            for (String cmd : commands) {
                if (cmd.trim().isEmpty()) continue;
                AutomationResult result = new AutomationResult();
                result.setScriptName(scriptName);
                result.setHost(host);
                try {
                    String output = ShellUtil.runShell(cmd);
                    result.setOutput(output);
                    result.setSuccess(!output.startsWith("error:"));
                } catch (Exception e) {
                    result.setOutput("error: " + e.getMessage());
                    result.setSuccess(false);
                }
                results.add(result);
            }
        } catch (Exception e) {
            AutomationResult result = new AutomationResult();
            result.setScriptName(scriptName);
            result.setHost(host);
            result.setOutput("error: " + e.getMessage());
            result.setSuccess(false);
            results.add(result);
        }
        return results;
    }
}