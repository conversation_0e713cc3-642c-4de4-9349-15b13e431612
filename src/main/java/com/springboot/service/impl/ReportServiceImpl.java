package com.springboot.service.impl;

import com.springboot.pojo.ReportInfo;
import com.springboot.pojo.SystemMetrics;
import com.springboot.service.ReportService;
import com.springboot.utils.ShellUtil;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Service
public class ReportServiceImpl implements ReportService {

    private final ConcurrentMap<String, ReportInfo> reportMap = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, Boolean> pdfStatusMap = new ConcurrentHashMap<>();

    @Value("${report.storage.path:./reports}")
    private String reportStoragePath;

    @Override
    public ReportInfo generateReport() {
        ReportInfo report = new ReportInfo();
        report.setReportId(UUID.randomUUID().toString());
        report.setTitle("系统实时巡检报告");

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date now = new Date();
        report.setGenerateTime(sdf.format(now));
        report.setStartTime(sdf.format(new Date(now.getTime() - 24 * 60 * 60 * 1000)));
        report.setEndTime(sdf.format(now));

        // 获取系统指标
        SystemMetrics metrics = getSystemMetrics();

        // 构建报告内容
        String content = new JSONObject()
                .put("summary", metrics.getSummary())
                .put("trend", metrics.getTrend())
                .put("cpuUsage", metrics.getCpuUsage())
                .put("memoryUsage", metrics.getMemoryUsage())
                .put("diskUsage", metrics.getDiskUsage())
                .put("alerts", metrics.getAlerts())
                .put("events", metrics.getEvents())
                .put("loadAvg", new JSONArray()
                        .put(metrics.getLoad1())
                        .put(metrics.getLoad5())
                        .put(metrics.getLoad15()))
                .put("userCount", metrics.getUserCount())
                .toString();

        report.setContent(content);
        report.setPdfUrl("/api/report/" + report.getReportId() + "/pdf");
        pdfStatusMap.put(report.getReportId(), false);

        reportMap.put(report.getReportId(), report);
        generatePdfReportAsync(report);

        return report;
    }

    @Async("reportExecutor")
    public void generatePdfReportAsync(ReportInfo report) {
        try {
            // 创建存储目录
            File storageDir = new File(reportStoragePath);
            if (!storageDir.exists()) {
                storageDir.mkdirs();
            }

            // 创建PDF文件
            String pdfFilePath = reportStoragePath + "/" + report.getReportId() + ".pdf";
            Document document = new Document(PageSize.A4);
            PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(pdfFilePath));
            document.open();

            // 设置中文字体支持
            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            Font titleFont = new Font(bfChinese, 24, Font.BOLD);
            Font headerFont = new Font(bfChinese, 16, Font.BOLD);
            Font subHeaderFont = new Font(bfChinese, 14, Font.BOLD);
            Font normalFont = new Font(bfChinese, 12, Font.NORMAL);

            // 添加标题
            Paragraph title = new Paragraph(report.getTitle(), titleFont);
            title.setAlignment(Element.ALIGN_CENTER);
            title.setSpacingAfter(20);
            document.add(title);

            // 添加生成时间
            Paragraph time = new Paragraph("生成时间: " + report.getGenerateTime(), normalFont);
            time.setAlignment(Element.ALIGN_CENTER);
            time.setSpacingAfter(20);
            document.add(time);

            // 添加分隔线
            document.add(new Paragraph("________________________________________________", normalFont));
            document.add(Chunk.NEWLINE);

            // 解析报告内容
            JSONObject contentObj = new JSONObject(report.getContent());

            // 添加系统概述
            Paragraph summaryTitle = new Paragraph("系统概述", headerFont);
            summaryTitle.setSpacingBefore(10);
            summaryTitle.setSpacingAfter(5);
            document.add(summaryTitle);

            Paragraph summaryContent = new Paragraph(contentObj.getString("summary"), normalFont);
            summaryContent.setIndentationLeft(10);
            summaryContent.setSpacingAfter(20);
            document.add(summaryContent);

            // 添加资源使用表格
            Paragraph resourceTitle = new Paragraph("资源使用情况", subHeaderFont);
            resourceTitle.setSpacingBefore(10);
            resourceTitle.setSpacingAfter(10);
            document.add(resourceTitle);

            PdfPTable resourceTable = new PdfPTable(2);
            resourceTable.setWidthPercentage(80);
            resourceTable.setSpacingBefore(5);
            resourceTable.setSpacingAfter(15);
            resourceTable.setWidths(new float[]{1, 2});

            // 表头
            PdfPCell cell1 = new PdfPCell(new Phrase("资源", headerFont));
            cell1.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell1.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell1.setPadding(8);
            resourceTable.addCell(cell1);

            PdfPCell cell2 = new PdfPCell(new Phrase("使用率", headerFont));
            cell2.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell2.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell2.setPadding(8);
            resourceTable.addCell(cell2);

            // 数据行
            resourceTable.addCell(createCell("CPU", normalFont));
            resourceTable.addCell(createCell(contentObj.getDouble("cpuUsage") + "%", normalFont));

            resourceTable.addCell(createCell("内存", normalFont));
            resourceTable.addCell(createCell(contentObj.getDouble("memoryUsage") + "%", normalFont));

            resourceTable.addCell(createCell("磁盘", normalFont));
            resourceTable.addCell(createCell(contentObj.getDouble("diskUsage") + "%", normalFont));

            document.add(resourceTable);

            // 添加系统指标表格
            Paragraph metricsTitle = new Paragraph("系统指标", subHeaderFont);
            metricsTitle.setSpacingBefore(10);
            metricsTitle.setSpacingAfter(10);
            document.add(metricsTitle);

            PdfPTable metricsTable = new PdfPTable(2);
            metricsTable.setWidthPercentage(80);
            metricsTable.setSpacingBefore(5);
            metricsTable.setSpacingAfter(15);
            metricsTable.setWidths(new float[]{1, 2});

            // 表头
            cell1 = new PdfPCell(new Phrase("指标", headerFont));
            cell1.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell1.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell1.setPadding(8);
            metricsTable.addCell(cell1);

            cell2 = new PdfPCell(new Phrase("值", headerFont));
            cell2.setBackgroundColor(BaseColor.LIGHT_GRAY);
            cell2.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell2.setPadding(8);
            metricsTable.addCell(cell2);

            // 数据行
            metricsTable.addCell(createCell("系统趋势", normalFont));
            metricsTable.addCell(createCell(contentObj.getString("trend"), normalFont));

            metricsTable.addCell(createCell("告警数量", normalFont));
            metricsTable.addCell(createCell(String.valueOf(contentObj.getInt("alerts")), normalFont));

            metricsTable.addCell(createCell("事件数量", normalFont));
            metricsTable.addCell(createCell(String.valueOf(contentObj.getInt("events")), normalFont));

            metricsTable.addCell(createCell("系统负载 (1/5/15分钟)", normalFont));
            metricsTable.addCell(createCell(String.format("%.2f / %.2f / %.2f",
                    contentObj.getJSONArray("loadAvg").getDouble(0),
                    contentObj.getJSONArray("loadAvg").getDouble(1),
                    contentObj.getJSONArray("loadAvg").getDouble(2)), normalFont));

            metricsTable.addCell(createCell("在线用户数", normalFont));
            metricsTable.addCell(createCell(String.valueOf(contentObj.getInt("userCount")), normalFont));

            document.add(metricsTable);

            // 添加页脚
            Paragraph footer = new Paragraph("系统巡检报告", normalFont);
            footer.setAlignment(Element.ALIGN_CENTER);
            footer.setSpacingBefore(30);
            document.add(footer);

            document.close();
            pdfStatusMap.put(report.getReportId(), true);
            System.out.println("PDF生成成功：" + pdfFilePath);

        } catch (Exception e) {
            e.printStackTrace();
            pdfStatusMap.put(report.getReportId(), false);
        }
    }

    @Override
    public List<ReportInfo> listReports(String startTime, String endTime) {
        List<ReportInfo> result = new ArrayList<>();
        for (ReportInfo info : reportMap.values()) {
            if ((startTime == null || info.getStartTime().compareTo(startTime) >= 0) &&
                    (endTime == null || info.getEndTime().compareTo(endTime) <= 0)) {
                result.add(info);
            }
        }
        return result;
    }

    @Override
    public ReportInfo getReportById(String reportId) {
        return reportMap.get(reportId);
    }

    @Override
    public byte[] getReportPdf(String reportId) {
        if (!pdfStatusMap.getOrDefault(reportId, false)) {
            return null; // PDF未生成
        }
        String pdfFilePath = reportStoragePath + "/" + reportId + ".pdf";
        File pdfFile = new File(pdfFilePath);
        if (pdfFile.exists()) {
            try {
                return Files.readAllBytes(pdfFile.toPath());
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    @Override
    public boolean isPdfAvailable(String reportId) {
        return pdfStatusMap.getOrDefault(reportId, false);
    }

    // 创建表格单元格的辅助方法
    private PdfPCell createCell(String text, Font font) {
        PdfPCell cell = new PdfPCell(new Phrase(text, font));
        cell.setPadding(6);
        cell.setBorderWidth(1);
        return cell;
    }

    // 获取系统指标的方法
    private SystemMetrics getSystemMetrics() {
        SystemMetrics metrics = new SystemMetrics();

        // 执行Shell命令获取系统指标
        String cpuOutput = ShellUtil.runShell("top -bn1 | grep 'Cpu(s)' | awk '{print $2 + $4}'");
        metrics.setCpuUsage(parseDouble(cpuOutput.trim()));

        String memOutput = ShellUtil.runShell("free | grep Mem | awk '{printf \"%.1f\", $3*100/$2 }'");
        metrics.setMemoryUsage(parseDouble(memOutput.trim()));

        String diskOutput = ShellUtil.runShell("df -h / | awk 'NR==2 {print $5}' | sed 's/%//'");
        metrics.setDiskUsage(parseDouble(diskOutput.trim()));

        String loadOutput = ShellUtil.runShell("cat /proc/loadavg | awk '{print $1, $2, $3}'");
        String[] loadValues = loadOutput.trim().split(" ");
        if (loadValues.length >= 3) {
            metrics.setLoad1(parseDouble(loadValues[0]));
            metrics.setLoad5(parseDouble(loadValues[1]));
            metrics.setLoad15(parseDouble(loadValues[2]));
        }

        String userOutput = ShellUtil.runShell("who | wc -l");
        metrics.setUserCount(parseInt(userOutput.trim()));

        String alertOutput = ShellUtil.runShell("grep -iE 'error|warning' /var/log/messages | wc -l");
        metrics.setAlerts(parseInt(alertOutput.trim()));

        String eventOutput = ShellUtil.runShell("find /var/log -type f -mtime -1 -name '*.log' -exec cat {} + | wc -l");
        metrics.setEvents(parseInt(eventOutput.trim()));

        // 设置健康状态摘要
        metrics.setSummary("健康状态良好");
        if (metrics.getCpuUsage() > 70 || metrics.getMemoryUsage() > 80 || metrics.getDiskUsage() > 85) {
            metrics.setSummary("系统资源压力较大");
        } else if (metrics.getAlerts() > 3) {
            metrics.setSummary("存在多个告警需要处理");
        }

        // 设置资源趋势
        metrics.setTrend("资源稳定");
        if (metrics.getCpuUsage() > 50 || metrics.getMemoryUsage() > 60) {
            metrics.setTrend("资源使用呈上升趋势");
        }

        return metrics;
    }

    // 安全转换字符串为double
    private double parseDouble(String value) {
        try {
            return Double.parseDouble(value);
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    // 安全转换字符串为int
    private int parseInt(String value) {
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}