package com.springboot.service;

import com.springboot.pojo.ServiceInfo;
import java.util.List;

/**
 * 服务管理接口，定义服务的查询与操作方法
 */
public interface ServiceService {
    /**
     * 获取所有服务列表
     * @return 服务信息列表
     */
    List<ServiceInfo> listServices();

    /**
     * 启动指定服务
     * @param name 服务名
     * @return 是否成功
     */
    boolean startService(String name);

    /**
     * 停止指定服务
     * @param name 服务名
     * @return 是否成功
     */
    boolean stopService(String name);

    /**
     * 重启指定服务
     * @param name 服务名
     * @return 是否成功
     */
    boolean restartService(String name);
}