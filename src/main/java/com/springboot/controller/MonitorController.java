package com.springboot.controller;

import com.springboot.pojo.MonitorInfo;
import com.springboot.service.MonitorService;
import com.springboot.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/monitor")
public class MonitorController {
    @Autowired
    private MonitorService monitorService;

    @GetMapping("")
    public Result<MonitorInfo> getMonitorInfo() {
        MonitorInfo info = monitorService.getMonitorInfo();
        return Result.success(info);
    }
}