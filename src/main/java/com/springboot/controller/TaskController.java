package com.springboot.controller;

import com.springboot.pojo.TaskInfo;
import com.springboot.service.TaskService;
import com.springboot.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/api/task")
public class TaskController {
    @Autowired
    private TaskService taskService;

    /** 查询所有任务 */
    @GetMapping("")
    public Result<List<TaskInfo>> listTasks() {
        return Result.success(taskService.listTasks());
    }


    /** 新增任务 */
    @PostMapping("")
    public Result<String> addTask(@RequestBody TaskInfo task) {
        boolean ok = taskService.addTask(task);
        return ok ? Result.success("添加成功") : Result.error("添加失败");
    }

    /** 删除任务 */
    @DeleteMapping("/{id}")
    public Result<String> deleteTask(@PathVariable String id) {
        boolean ok = taskService.deleteTask(id);
        return ok ? Result.success("删除成功") : Result.error("删除失败");
    }
}