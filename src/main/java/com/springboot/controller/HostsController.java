package com.springboot.controller;

import com.springboot.pojo.HostsEntry;
import com.springboot.service.HostsService;
import com.springboot.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * hosts管理接口
 */
@RestController
@RequestMapping("/api/hosts")
public class HostsController {
    @Autowired
    private HostsService hostsService;

    /**
     * 获取所有hosts条目
     */
    @GetMapping("/list")
    public Result<List<HostsEntry>> listHosts() {
        return Result.success(hostsService.listHosts());
    }


    /**
     * 添加hosts条目
     */
    @PostMapping("/add")
    public Result addHost(@RequestBody HostsEntry entry) {
        hostsService.addHost(entry);
        return Result.success("添加成功");
    }

    /**
     * 删除hosts条目
     */
    @PostMapping("/delete")
    public Result deleteHost(@RequestBody HostsEntry entry) {
        hostsService.deleteHost(entry);
        return Result.success("删除成功");
    }
}