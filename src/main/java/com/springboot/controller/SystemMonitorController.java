package com.springboot.controller;

import com.springboot.pojo.*;
import com.springboot.service.SystemMonitorService;
import com.springboot.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/monitor")
public class SystemMonitorController {

    @Autowired
    private SystemMonitorService monitorService;

    @GetMapping("/system")
    public Result<SystemInfo> getSystemInfo() {
        SystemInfo info = monitorService.getSystemInfo();
        return Result.success(info);
    }

    @GetMapping("/cpu")
    public Result<CpuInfo> getCpuInfo() {
        CpuInfo info = monitorService.getCpuInfo();
        return Result.success(info);
    }

    @GetMapping("/memory")
    public Result<MemoryInfo> getMemoryInfo() {
        MemoryInfo info = monitorService.getMemoryInfo();
        return Result.success(info);
    }

    @GetMapping("/processes")
    public Result<ProcessInfoVo[]> getTopProcesses(@RequestParam(defaultValue = "10") int count) {
        ProcessInfoVo[] processes = monitorService.getTopProcesses(count);
        return Result.success(processes);
    }
}    