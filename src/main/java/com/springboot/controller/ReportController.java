package com.springboot.controller;

import com.springboot.pojo.ReportInfo;
import com.springboot.service.ReportService;
import com.springboot.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/report")
public class ReportController {
    @Autowired
    private ReportService reportService;

    /**
     * 获取报告列表
     */
    @GetMapping("")
    public Result<List<ReportInfo>> listReports(@RequestParam(required = false) String startTime,
                                                @RequestParam(required = false) String endTime) {
        return Result.success(reportService.listReports(startTime, endTime));
    }

    /**
     * 获取报告详情
     */
    @GetMapping("/{reportId}")
    public Result<ReportInfo> getReportById(@PathVariable String reportId) {
        ReportInfo report = reportService.getReportById(reportId);
        if (report != null) {
            return Result.success(report);
        } else {
            return Result.error("报告不存在");
        }
    }

    /**
     * 生成新报告
     */
    @PostMapping("/generate")
    public Result<ReportInfo> generateReport() {
        return Result.success(reportService.generateReport());
    }

    /**
     * 下载报告PDF
     */
    @GetMapping("/{reportId}/pdf")
    public ResponseEntity<?> downloadReportPdf(@PathVariable String reportId) {
        // 检查报告是否存在
        ReportInfo report = reportService.getReportById(reportId);
        if (report == null) {
            return ResponseEntity.notFound().build();
        }

        // 检查PDF是否生成完成
        if (!reportService.isPdfAvailable(reportId)) {
            return ResponseEntity.status(HttpStatus.ACCEPTED)
                    .body(Result.success("PDF生成中，请稍后再试"));
        }

        // 获取PDF内容
        byte[] pdfContent = reportService.getReportPdf(reportId);
        if (pdfContent == null) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Result.error("PDF生成失败"));
        }

        // 设置响应头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_PDF);
        headers.setContentDispositionFormData("attachment", "report_" + reportId + ".pdf");
        headers.setContentLength(pdfContent.length);

        return new ResponseEntity<>(pdfContent, headers, HttpStatus.OK);
    }
}