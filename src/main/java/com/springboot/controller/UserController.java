package com.springboot.controller;

import com.springboot.pojo.UserInfo;
import com.springboot.service.UserService;
import com.springboot.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * Linux服务器用户管理接口
 */
@RestController
@RequestMapping("/api/user")
public class UserController {
    @Autowired
    private UserService userService;

    /**
     * 查询所有用户
     */
    @GetMapping
    public Result<List<UserInfo>> listUsers() {
        List<UserInfo> users = userService.listUsers();
        return Result.success(users);
    }
}