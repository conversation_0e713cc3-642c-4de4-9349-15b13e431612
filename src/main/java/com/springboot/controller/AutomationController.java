package com.springboot.controller;

import com.springboot.pojo.AutomationResult;
import com.springboot.service.AutomationService;
import com.springboot.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

@RestController
@RequestMapping("/api/automation")
public class AutomationController {
    @Autowired
    private AutomationService automationService;

    /**
     * 上传脚本并批量执行
     */
    @PostMapping("/upload")
    public Result<List<AutomationResult>> uploadAndExecute(@RequestParam("script") MultipartFile script) {
        List<AutomationResult> results = automationService.uploadAndExecute(script);
        return Result.success(results);
    }
}