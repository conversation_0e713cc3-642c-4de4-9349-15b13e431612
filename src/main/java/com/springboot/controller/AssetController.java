package com.springboot.controller;

import com.springboot.pojo.AssetInfo;
import com.springboot.service.AssetService;
import com.springboot.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/asset")
public class AssetController {
    @Autowired
    private AssetService assetService;

    @GetMapping("")
    public Result<AssetInfo> getAssetInfo() {
        AssetInfo info = assetService.getAssetInfo();
        return Result.success(info);
    }
}