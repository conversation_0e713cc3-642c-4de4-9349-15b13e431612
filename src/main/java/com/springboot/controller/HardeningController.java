package com.springboot.controller;

import com.springboot.pojo.HardeningResult;
import com.springboot.service.HardeningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/hardening")
public class HardeningController {
    @Autowired
    private HardeningService hardeningService;

    @GetMapping("/checkWeakPassword")
    public Map<String, Object> checkWeakPassword() {
        List<HardeningResult> list = hardeningService.checkWeakPassword();
        Map<String, Object> res = new HashMap<>();
        res.put("code", 200);
        res.put("data", list);
        return res;
    }

    @GetMapping("/checkSystemPatch")
    public Map<String, Object> checkSystemPatch() {
        List<HardeningResult> list = hardeningService.checkSystemPatch();
        Map<String, Object> res = new HashMap<>();
        res.put("code", 200);
        res.put("data", list);
        return res;
    }

    @GetMapping("/checkOpenPorts")
    public Map<String, Object> checkOpenPorts() {
        List<HardeningResult> list = hardeningService.checkOpenPorts();
        Map<String, Object> res = new HashMap<>();
        res.put("code", 200);
        res.put("data", list);
        return res;
    }

    @GetMapping("/checkOtherItems")
    public Map<String, Object> checkOtherItems() {
        List<HardeningResult> list = hardeningService.checkOtherItems();
        Map<String, Object> res = new HashMap<>();
        res.put("code", 200);
        res.put("data", list);
        return res;
    }
}