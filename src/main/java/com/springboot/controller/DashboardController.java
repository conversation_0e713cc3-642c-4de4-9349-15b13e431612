package com.springboot.controller;

import com.springboot.pojo.DashboardInfo;
import com.springboot.service.DashboardService;
import com.springboot.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/dashboard")
public class DashboardController {
    @Autowired
    private DashboardService dashboardService;

    @GetMapping("")
    public Result<DashboardInfo> getDashboardInfo() {
        DashboardInfo info = dashboardService.getDashboardInfo();
        return Result.success(info);
    }
}