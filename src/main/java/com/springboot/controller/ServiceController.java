package com.springboot.controller;

import com.springboot.pojo.ServiceInfo;
import com.springboot.service.ServiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务管理控制器，提供服务相关REST接口
 */
@RestController
@RequestMapping("/api/service")
public class ServiceController {
    @Autowired
    private ServiceService serviceService;

    /**
     * 获取服务列表
     */
    @GetMapping("/list")
    public Map<String, Object> listServices() {
        List<ServiceInfo> list = serviceService.listServices();
        Map<String, Object> res = new HashMap<>();
        res.put("code", 200);
        res.put("data", list);
        return res;
    }

    /**
     * 启动服务
     */
    @PostMapping("/start")
    public Map<String, Object> startService(@RequestBody Map<String, String> params) {
        String name = params.get("name");
        boolean ok = serviceService.startService(name);
        Map<String, Object> res = new HashMap<>();
        res.put("code", ok ? 200 : 500);
        res.put("msg", ok ? "服务已启动" : "启动失败");
        return res;
    }

    /**
     * 停止服务
     */
    @PostMapping("/stop")
    public Map<String, Object> stopService(@RequestBody Map<String, String> params) {
        String name = params.get("name");
        boolean ok = serviceService.stopService(name);
        Map<String, Object> res = new HashMap<>();
        res.put("code", ok ? 200 : 500);
        res.put("msg", ok ? "服务已停止" : "停止失败");
        return res;
    }

    /**
     * 重启服务
     */
    @PostMapping("/restart")
    public Map<String, Object> restartService(@RequestBody Map<String, String> params) {
        String name = params.get("name");
        boolean ok = serviceService.restartService(name);
        Map<String, Object> res = new HashMap<>();
        res.put("code", ok ? 200 : 500);
        res.put("msg", ok ? "服务已重启" : "重启失败");
        return res;
    }
}