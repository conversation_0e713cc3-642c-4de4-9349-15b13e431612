package com.springboot.controller;

import com.springboot.utils.Result;
import com.springboot.utils.ShellUtil;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/run")
public class RunCommand {

    @PostMapping
    public Map<String, Object> handlePostRequest(@RequestBody Map<String, Object> requestBody) {
        System.out.println(requestBody.toString());
        // 验证输入是否包含必要的字段
        if (!requestBody.containsKey("command")) {
            return createErrorResponse("请求体必须包含command字段");
        }

        // 提取命令信息
        String command = (String) requestBody.get("command");
        String name = (String) requestBody.getOrDefault("name", "");

        Map<String, Object> data = new HashMap<>();
        data.put("command", command);
        data.put("name", name);

        Map<String, Object> executeResult = new HashMap<>();

        try {
            String s = ShellUtil.runShell(command);
            System.out.println(s);
            executeResult.put("stdout", s);
            executeResult.put("stderr", "");
            executeResult.put("error", "");
            executeResult.put("success", true);
        } catch (Exception e) {
            executeResult.put("stdout", "");
            executeResult.put("stderr", "");
            executeResult.put("error", "命令未执行");
            executeResult.put("success", false);
        }

        data.put("execute", executeResult);
        Map<String, Object> response = new HashMap<>();
        response.put("data", data);
        response.put("isOK", true);

        return response;
    }

    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> data = new HashMap<>();
        data.put("command", "");
        data.put("name", "");
        Map<String, Object> executeResult = new HashMap<>();
        executeResult.put("stdout", "");
        executeResult.put("stderr", "");
        executeResult.put("error", message);
        executeResult.put("success", false);
        data.put("execute", executeResult);
        Map<String, Object> response = new HashMap<>();
        response.put("data", data);
        response.put("isOK", false);
        return response;
    }
}