package com.springboot.controller;

import com.springboot.pojo.PortCheckResult;
import com.springboot.pojo.PortScanResult;
import com.springboot.service.PortService;
import com.springboot.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 端口检测与扫描接口
 */
@RestController
@RequestMapping("/api/port")
public class PortController {
    @Autowired
    private PortService portService;

    /**
     * 端口检测
     */
    @PostMapping("/check")
    public Result<PortCheckResult> checkPort(@RequestParam int port) {
        return Result.success(portService.checkPort("127.0.0.1", port));
    }

    /**
     * 端口扫描
     */
    @PostMapping("/scan")
    public Result<List<PortScanResult>> scanPorts() {
        return Result.success(portService.scanPorts("127.0.0.1"));
    }
}