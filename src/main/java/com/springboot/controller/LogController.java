package com.springboot.controller;

import com.springboot.pojo.LogInfo;
import com.springboot.service.LogService;
import com.springboot.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/log")
public class LogController {
    @Autowired
    private LogService logService;

    @GetMapping("")
    public Result<LogInfo> getLogInfo() {
        LogInfo info = logService.getLogInfo();
        return Result.success(info);
    }
}