package com.springboot.controller;

import com.springboot.pojo.ProcessInfo;
import com.springboot.service.ProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/process")
public class ProcessController {
    @Autowired
    private ProcessService processService;

    @GetMapping("/list")
    public Map<String, Object> listProcesses() {
        List<ProcessInfo> list = processService.listProcesses();
        Map<String, Object> res = new HashMap<>();
        res.put("code", 200);
        res.put("data", list);
        return res;
    }

    @PostMapping("/kill")
    public Map<String, Object> killProcess(@RequestParam int pid) {
        boolean ok = processService.killProcess(pid);
        Map<String, Object> res = new HashMap<>();
        res.put("code", ok ? 200 : 500);
        res.put("msg", ok ? "进程已终止" : "终止失败");
        return res;
    }

    @PostMapping("/start")
    public Map<String, Object> startProcess(@RequestParam String command) {
        boolean ok = processService.startProcess(command);
        Map<String, Object> res = new HashMap<>();
        res.put("code", ok ? 200 : 500);
        res.put("msg", ok ? "进程已启动" : "启动失败");
        return res;
    }

    @PostMapping("/restart")
    public Map<String, Object> restartProcess(@RequestParam int pid, @RequestParam String command) {
        boolean ok = processService.restartProcess(pid, command);
        Map<String, Object> res = new HashMap<>();
        res.put("code", ok ? 200 : 500);
        res.put("msg", ok ? "进程已重启" : "重启失败");
        return res;
    }
}