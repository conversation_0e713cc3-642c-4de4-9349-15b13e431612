package com.springboot.controller;

import com.springboot.pojo.FirewallRule;
import com.springboot.service.FirewallService;
import com.springboot.utils.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 防火墙规则管理接口
 */
@RestController
@RequestMapping("/api/firewall")
public class FirewallController {
    @Autowired
    private FirewallService firewallService;

    /** 查询所有防火墙规则 */
    @GetMapping("")
    public Result<List<FirewallRule>> listRules() {
        List<FirewallRule> rules = firewallService.listRules();
        return Result.success(rules);
    }

    /** 添加防火墙规则 */
    @PostMapping("")
    public Result<String> addRule(@RequestBody FirewallRule rule) {
        boolean ok = firewallService.addRule(rule);
        return ok ? Result.success("添加成功") : Result.error("添加失败");
    }

}