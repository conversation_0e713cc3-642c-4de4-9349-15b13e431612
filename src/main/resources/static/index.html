<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>麒麟智能运维系统</title>
  <!-- Tailwind CSS CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Font Awesome CDN -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Material Icons CDN -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <!-- 统一数据文件 -->
  <script src="/data/data.js"></script>
  <!-- 预留自定义样式和脚本目录 -->
  <!-- <link rel="stylesheet" href="/css/custom.css"> -->
  <!-- <script src="/js/custom.js"></script> -->
  <link rel="Shortcut Icon" href="./favicon.ico" type="image/x-icon" />

  <style>
    .table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .table th,
    .table td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #333;
    }

    .table th {
      background-color: #2e3440;
      color: #fff;
    }

    .table tr:hover {
      background-color: #2e3e50;
    }

    .table td {
      padding: 8px 12px; /* 增加单元格的内边距 */
    }

    .table .operation-btn {
      background-color: transparent;
      border: 1px solid #3b82f6;
      color: black;
      border-radius: 4px;
      padding: 4px 8px;
      margin-right: 5px; /* 按钮之间的间隙 */
      cursor: pointer;
      transition: all 0.3s;
    }

    .table .operation-btn:hover {
      background-color: #3b82f6;
      color: #fff;
    }

    .table .operation-btn:active {
      background-color: #2563eb;
    }
    .rule-item {
      font-size: 1rem; /* 字体大小 */
      color: #e2e8f0; /* 字体颜色 */
      margin-bottom: 0.5rem; /* 每行之间的间距 */
    }
    body { background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%); }
    .bento { box-shadow: 0 4px 32px 0 rgba(0,0,0,0.15); border-radius: 1.5rem; }
    .bento:hover { box-shadow: 0 8px 48px 0 rgba(0,0,0,0.25); }
    .highlight { background: linear-gradient(90deg,rgba(56,189,248,0.7),rgba(59,130,246,0.3)); }
    .big-title { font-size: 2.8rem; font-weight: bold; letter-spacing: 2px; color: #f1f5f9; text-shadow: 0 2px 12px rgba(16, 185, 255, 0.25), 0 1px 0 #0ea5e9; background: linear-gradient(90deg, #38bdf8 10%, #6366f1 90%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
    .en-title { font-size: 1rem; color: #94a3b8; }
    .bento-img { object-fit: cover; border-radius: 1rem; }
    .bento-icon { font-size: 2.5rem; color: #38bdf8; }
    .bento-btn {
      transition: transform 0.2s, box-shadow 0.2s, background 0.3s;
      background: linear-gradient(120deg, rgba(56,189,248,0.85) 0%, rgba(59,130,246,0.45) 100%);
      box-shadow: 0 2px 16px 0 rgba(56,189,248,0.15), 0 1.5px 8px 0 rgba(59,130,246,0.10);
      border: 2px solid rgba(56,189,248,0.25);
      color: #fff;
      font-weight: bold;
      letter-spacing: 1px;
      font-size: 1.25rem;
      padding: 1.1rem 1.5rem;
      border-radius: 1.2rem;
      position: relative;
      overflow: hidden;
    }
    .bento-btn::before {
      content: '';
      position: absolute;
      left: 0; top: 0; right: 0; bottom: 0;
      background: linear-gradient(90deg,rgba(255,255,255,0.08) 0%,rgba(56,189,248,0.12) 100%);
      opacity: 0.7;
      z-index: 0;
      border-radius: 1.2rem;
      pointer-events: none;
      transition: opacity 0.3s;
    }
    .bento-btn:hover {
      transform: scale(1.08) translateY(-2px);
      box-shadow: 0 8px 32px 0 rgba(56,189,248,0.25), 0 2px 16px 0 rgba(59,130,246,0.18);
      background: linear-gradient(120deg, rgba(59,130,246,0.95) 0%, rgba(56,189,248,0.7) 100%);
      border-color: #38bdf8;
    }
    .bento-btn:active {
      transform: scale(0.97);
      box-shadow: 0 2px 8px 0 rgba(56,189,248,0.10);
    }
    .bento-btn > * {
      position: relative;
      z-index: 1;
    }
    .scroll-animate { transition: all 0.7s cubic-bezier(.4,0,.2,1); }
  </style>
</head>
<body class="min-h-screen flex flex-col">
<div class="flex flex-1">
  <!-- 左侧导航 -->
  <aside class="w-64 bg-slate-900 text-white flex flex-col py-8 px-4 shadow-xl">
    <div class="mb-10">
      <span class="big-title" style="color:#fff;background:none;-webkit-text-fill-color:unset;text-shadow:none;">麒麟保安</span>
      <div class="en-title">Qilin BaoAn</div>
    </div>
    <nav id="category-nav" class="flex flex-col gap-2"></nav>
  </aside>
  <!-- 右侧内容区 -->
  <main class="flex-1 p-10 overflow-y-auto">
    <div id="main-content"></div>
  </main>
</div>
<footer class="text-center text-slate-400 py-4">© 麒麟保安智能运维系统 Qilin BaoAn</footer>
<script>
  // 动态渲染导航和功能区
  function renderCategories(selectedId) {
    const nav = document.getElementById('category-nav');
    nav.innerHTML = '';
    categories.forEach(cat => {
      const btn = document.createElement('button');
      btn.className = 'bento-btn flex flex-row items-center w-full h-14 px-4 py-2 highlight transition-all duration-200 text-left rounded-lg mb-1' + (selectedId === cat.id ? ' ring-2 ring-cyan-400 bg-gradient-to-r from-cyan-500/80 to-blue-500/60' : ' bg-slate-800/80 hover:bg-cyan-700/40');
      btn.innerHTML = `<i class="fa ${cat.icon} bento-icon mb-3" style="font-size:1.3rem;"></i><span class="font-bold text-base">${cat.name}</span><span class="en-title ml-2 text-xs">${cat.en}</span>`;
      btn.onclick = () => { renderFeatures(cat); renderCategories(cat.id); };
      nav.appendChild(btn);
    });
  }
  categories.push({
    id: 'ai',
    name: '智能运维',
    en: 'AI OP',
    icon: 'fa-robot'
  });


  function renderFeatures(category) {
    if (category.id === 'ai'){
      window.location.href = '/ai.html';
    }
    const main = document.getElementById('main-content');
    main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div>`;
    const grid = document.createElement('div');
    grid.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8';
    category.features.forEach(f => {
      const card = document.createElement('div');
      card.className = 'bento p-6 bg-slate-800 flex flex-col items-center bento-btn scroll-animate';
      card.innerHTML = `
          <i class="fa ${f.icon} bento-icon mb-2"></i>
          <div class="font-extrabold text-2xl text-white mb-1">${f.name}</div>
          <div class="en-title mb-2">${f.en}</div>
        `;
      // 引入ECharts CDN
      if(!document.getElementById('echarts-cdn')){
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js';
        script.id = 'echarts-cdn';
        document.head.appendChild(script);
      }
      // 新增：为实时监控按钮添加点击事件
      if(f.id === 'monitor') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-white mb-4'>正在加载监控数据...</div>`;
          fetch('/api/monitor').then(res => res.json()).then(res => {
            if(res.code === 200 && res.data) {
              main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div>` +
                      `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                      `<div class='font-bold text-lg mb-2'>CPU使用率</div><pre class='bg-slate-900 rounded p-2 mb-2'>${res.data.cpuUsage}</pre>` +
                      `<div class='font-bold text-lg mb-2'>内存使用</div><pre class='bg-slate-900 rounded p-2 mb-2'>${res.data.memUsage}</pre>` +
                      `<div class='font-bold text-lg mb-2'>磁盘使用</div><pre class='bg-slate-900 rounded p-2'>${res.data.diskUsage}</pre>` +
                      `<div class='font-bold text-lg mb-2'>告警信息</div><pre class='bg-slate-900 rounded p-2 mb-2 text-red-400'>${res.data.alarm||'-'}</pre>` +
                      `<div class='font-bold text-lg mb-2'>服务状态</div><pre class='bg-slate-900 rounded p-2'>${res.data.serviceStatus||'-'}</pre>` +
                      `</div>`;
            } else {
              main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-red-400'>监控数据获取失败：${res.message||'未知错误'}</div>`;
            }
          }).catch(err => {
            main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-red-400'>监控数据获取异常</div>`;
          });
        };
      }
      // 新增：为日志查询按钮添加点击事件
      if(f.id === 'log') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-white mb-4'>正在加载日志...</div>`;
          fetch('/api/log').then(res => res.json()).then(res => {
            if(res.code === 200 && res.data) {
              main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div>` +
                      `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                      `<div class='font-bold text-lg mb-2'>日志内容（最近100行）</div><pre class='bg-slate-900 rounded p-2 mb-2' style='max-height:400px;overflow:auto;'>${res.data.logs}</pre>` +
                      `</div>`;
            } else {
              main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-red-400'>日志获取失败：${res.message||'未知错误'}</div>`;
            }
          }).catch(err => {
            main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-red-400'>日志获取异常</div>`;
          });
        };
      }
      // 新增：为资产查询按钮添加点击事件
      if(f.id === 'asset') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-white mb-4'>正在加载资产信息...</div>`;
          fetch('/api/asset').then(res => res.json()).then(res => {
            if(res.code === 200 && res.data) {
              main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div>` +
                      `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                      `<div class='font-bold text-lg mb-2'>资产信息</div><pre class='bg-slate-900 rounded p-2 mb-2' style='max-height:400px;overflow:auto;'>${res.data.assets}</pre>` +
                      `</div>`;
            } else {
              main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-red-400'>资产信息获取失败：${res.message||'未知错误'}</div>`;
            }
          }).catch(err => {
            main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-red-400'>资产信息获取异常</div>`;
          });
        };
      }
      // 新增：为运维大盘按钮添加点击事件
      if(f.id === 'dashboard') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-white mb-4'>正在加载大盘数据...</div>`;
          fetch('/api/dashboard').then(res => res.json()).then(res => {
            if(res.code === 200 && res.data) {
              // 数字翻牌区
              const flipCards = `
                  <div class='grid grid-cols-5 gap-4 mb-6'>
                    <div class='flip-card'><div class='flip-title'>CPU使用率</div><div class='flip-value text-2xl font-bold'>${res.data.cpuUsage||'-'}</div></div>
                    <div class='flip-card'><div class='flip-title'>内存使用</div><div class='flip-value text-2xl font-bold'>${res.data.memUsage||'-'}</div></div>
                    <div class='flip-card'><div class='flip-title'>磁盘使用</div><div class='flip-value text-2xl font-bold'>${res.data.diskUsage||'-'}</div></div>
                    <div class='flip-card'><div class='flip-title'>登录用户数</div><div class='flip-value text-2xl font-bold'>${res.data.userCount||'-'}</div></div>
                    <div class='flip-card'><div class='flip-title'>平均负载</div><div class='flip-value text-2xl font-bold'>${res.data.loadAverage||'-'}</div></div>
                  </div>`;
              // 图表区
              const chartArea = `<div id='dashboard-charts' class='mb-6'>
                  <div id='cpu-pie' style='width:260px;height:220px;display:inline-block;'></div>
                  <div id='mem-pie' style='width:260px;height:220px;display:inline-block;'></div>
                  <div id='disk-pie' style='width:260px;height:220px;display:inline-block;'></div>
                </div>`;
              // 汇总信息
              const summaryBlock = `<div class='bento p-6 bg-slate-800 text-white mb-4'>`+
                      `<div class='font-bold text-lg mb-2'>运维大盘原始信息</div><pre class='bg-slate-900 rounded p-2 mb-2' style='max-height:400px;overflow:auto;'>${res.data.summary}</pre>`+
                      `</div>`;
              main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div>` +
                      flipCards + chartArea + summaryBlock;
              // 渲染ECharts图表
              setTimeout(()=> {
                if(window.echarts){
                  // CPU饼图
                  let cpuVal = parseFloat((res.data.cpuUsage||'0').replace('%',''));
                  if(isNaN(cpuVal)) cpuVal=0;
                  let cpuChart = echarts.init(document.getElementById('cpu-pie'));
                  cpuChart.setOption({
                    title:{text:'CPU使用率',left:'center',top:10,textStyle:{color:'#fff',fontSize:16}},
                    tooltip:{},
                    series:[{
                      type:'pie',radius:['50%','80%'],avoidLabelOverlap:false,label:{show:true,formatter:'{d}%'},data:[{value:cpuVal,name:'已用'},{value:100-cpuVal,name:'空闲'}],
                      color:['#3b82f6','#64748b']
                    }]
                  });
                  // 内存饼图
                  let memUsed=0,memTotal=1;
                  if(res.data.memUsage){
                    let m = res.data.memUsage.match(/(\d+)\/(\d+)/);
                    if(m){memUsed=parseInt(m[1]);memTotal=parseInt(m[2]);}
                  }
                  let memChart = echarts.init(document.getElementById('mem-pie'));
                  memChart.setOption({
                    title:{text:'内存使用',left:'center',top:10,textStyle:{color:'#fff',fontSize:16}},
                    tooltip:{},
                    series:[{
                      type:'pie',radius:['50%','80%'],avoidLabelOverlap:false,label:{show:true,formatter:'{d}%'},data:[{value:memUsed,name:'已用'},{value:memTotal-memUsed,name:'空闲'}],
                      color:['#06d6a0','#64748b']
                    }]
                  });
                  // 磁盘饼图
                  let diskUsed=0,diskTotal=1;
                  if(res.data.diskUsage){
                    let d = res.data.diskUsage.match(/([\d.]+)\/([\d.]+)/);
                    if(d){diskUsed=parseFloat(d[1]);diskTotal=parseFloat(d[2]);}
                  }
                  let diskChart = echarts.init(document.getElementById('disk-pie'));
                  diskChart.setOption({
                    title:{text:'磁盘使用',left:'center',top:10,textStyle:{color:'#fff',fontSize:16}},
                    tooltip:{},
                    series:[{
                      type:'pie',radius:['50%','80%'],avoidLabelOverlap:false,label:{show:true,formatter:'{d}%'},data:[{value:diskUsed,name:'已用'},{value:diskTotal-diskUsed,name:'空闲'}],
                      color:['#f59e42','#64748b']
                    }]
                  });
                }
              },500);
            } else {
              main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-red-400'>大盘数据获取失败：${res.message||'未知错误'}</div>`;
            }
          }).catch(err => {
            main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-red-400'>大盘数据获取异常</div>`;
          });
        };
      }
      // 新增：为趋势分析按钮添加点击事件
      if(f.id === 'trend') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          // 动态生成趋势分析页面内容
          main.innerHTML = `
      <div class='big-title mb-6 drop-shadow-lg'>趋势分析<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>Real-time Trends</span></div>

          <div class='bento p-4 bg-slate-800 text-white mb-6 flex items-center justify-between'>
          <div class='font-bold text-lg flex items-center'>
          <span>实时系统监控</span>
          <span class='ml-3 text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full flex items-center'>
          <span class='w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse'></span>已连接
          </span>
          </div>
          <button id='refresh-now' class='bento-btn px-4 py-2 text-sm'>
          <i class='fa fa-refresh mr-1'></i>立即刷新
          </button>
          </div>
          <div class='grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6'>
          <div class='bento p-6 bg-slate-800 text-white'>
          <div class='font-bold text-xl mb-4 flex justify-between items-center'>
          <span>CPU使用分布</span>
          <div class='text-sm text-slate-400'>单位: %</div>
          </div>
          <div id='cpu-distribution-chart' style='width:100%;height:350px;'></div>
          </div>
          <div class='bento p-6 bg-slate-800 text-white'>
          <div class='font-bold text-xl mb-4 flex justify-between items-center'>
          <span>内存使用情况</span>
          <div class='text-sm text-slate-400'>单位: MB</div>
          </div>
          <div id='memory-usage-chart' style='width:100%;height:350px;'></div>
          </div>
          </div>
          <div class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6'>
          <div class='bento p-6 bg-slate-800 text-white'>
          <div class='font-bold text-lg mb-2'>CPU使用率</div>
          <div class='text-3xl font-bold text-cyan-400 mb-1' id='cpu-usage'>0%</div>
          <div class='text-sm text-slate-400'>系统: <span id='cpu-system'>0%</span> | 用户: <span id='cpu-user'>0%</span></div>
          </div>
          <div class='bento p-6 bg-slate-800 text-white'>
          <div class='font-bold text-lg mb-2'>内存使用率</div>
          <div class='text-3xl font-bold text-cyan-400 mb-1' id='mem-usage'>0%</div>
          <div class='text-sm text-slate-400'>已用: <span id='mem-used'>0 MB</span> | 可用: <span id='mem-available'>0 MB</span></div>
          </div>
          <div class='bento p-6 bg-slate-800 text-white'>
          <div class='font-bold text-lg mb-2'>交换空间</div>
          <div class='text-3xl font-bold text-cyan-400 mb-1' id='swap-usage'>0%</div>
          <div class='text-sm text-slate-400'>已用: <span id='swap-used'>0 MB</span> | 总容量: <span id='swap-total'>0 MB</span></div>
          </div>
          <div class='bento p-6 bg-slate-800 text-white'>
          <div class='font-bold text-lg mb-2'>系统负载</div>
          <div class='text-3xl font-bold text-cyan-400 mb-1' id='load-average'>0.00</div>
          <div class='text-sm text-slate-400'>5分钟: <span id='load-5'>0.00</span> | 15分钟: <span id='load-15'>0.00</span></div>
          </div>
          </div>
          <div class='bento p-6 bg-slate-800 text-white'>
          <div class='font-bold text-xl mb-4'>高CPU使用率进程</div>
          <div class='overflow-x-auto'>
          <table class='min-w-full'>
          <thead>
          <tr class='bg-slate-700 text-left text-xs font-medium text-gray-400 uppercase tracking-wider'>
          <th class='px-4 py-3 rounded-tl-lg'>PID</th>
          <th class='px-4 py-3'>用户</th>
          <th class='px-4 py-3'>CPU%</th>
          <th class='px-4 py-3'>内存%</th>
          <th class='px-4 py-3 rounded-tr-lg'>命令</th>
          </tr>
          </thead>
          <tbody class='bg-slate-800 divide-y divide-slate-700' id='processes-table-body'>
          </tbody>
          </table>
          </div>
          </div>
          `;

    // 引入ECharts
    if(!window.echarts) {
      const script = document.createElement('script');
      script.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js';
      script.onload = initCharts;
      document.head.appendChild(script);
    } else {
      initCharts();
    }

    // 初始化图表
    function initCharts() {
      // 创建图表实例
      const cpuChart = echarts.init(document.getElementById('cpu-distribution-chart'));
      const memoryChart = echarts.init(document.getElementById('memory-usage-chart'));

      // 图表通用配置
      const chartOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        textStyle: {
          color: '#e2e8f0'
        },
        label: {
          color: '#e2e8f0'
        }
      };

      // CPU分布图表配置
      cpuChart.setOption({
        ...chartOption,
        title: {
          text: 'CPU使用分布',
          left: 'center',
          textStyle: { color: '#e2e8f0' }
        },
        series: [{
          name: 'CPU使用率',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#2e3440',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '16',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: []
        }]
      });

      // 内存使用图表配置
      memoryChart.setOption({
        ...chartOption,
        title: {
          text: '内存使用情况',
          left: 'center',
          textStyle: { color: '#e2e8f0' }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: ['已使用', '空闲', '缓存', '可用'],
          axisLine: { lineStyle: { color: '#94a3b8' } },
          axisLabel: { color: '#e2e8f0' }
        },
        yAxis: {
          type: 'value',
          axisLine: { lineStyle: { color: '#94a3b8' } },
          axisLabel: {
            color: '#e2e8f0',
            formatter: '{value} MB'
          }
        },
        series: [{
          name: '内存(MB)',
          type: 'bar',
          barWidth: '60%',
          data: [],
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#38bdf8' },
              { offset: 1, color: '#3b82f6' }
            ])
          }
        }]
      });

      // 加载实时数据
      loadRealtimeData();

      // 立即刷新按钮事件
      document.getElementById('refresh-now').addEventListener('click', loadRealtimeData);

      // 定时刷新数据 (每5秒)
      const refreshInterval = setInterval(loadRealtimeData, 5000);

      // 窗口大小变化时重绘图表
      window.addEventListener('resize', () => {
        cpuChart.resize();
        memoryChart.resize();
      });

      // 清理函数 (离开页面时)
      window.addEventListener('beforeunload', () => {
        clearInterval(refreshInterval);
      });

      // 加载实时数据
      function loadRealtimeData() {
        // 显示加载状态
        cpuChart.showLoading({ text: '加载中...', textStyle: { color: '#fff' } });
        memoryChart.showLoading({ text: '加载中...', textStyle: { color: '#fff' } });

        // 并行请求所有需要的数据
        Promise.all([
          fetch('/api/monitor/system').then(res => res.json()),
          fetch('/api/monitor/processes?count=5').then(res => res.json())
        ]).then(([systemRes, processesRes]) => {
          // 隐藏加载状态
          cpuChart.hideLoading();
          memoryChart.hideLoading();

          if (systemRes.code === 200 && systemRes.data) {
            const systemInfo = systemRes.data;
            const cpuInfo = systemInfo.cpuInfo;
            const memoryInfo = systemInfo.memoryInfo;

            // 更新CPU数据
            updateCpuData(cpuInfo);

            // 更新内存数据
            updateMemoryData(memoryInfo);

            // 更新系统负载数据
            document.getElementById('load-average').textContent = systemInfo.loadAverage1.toFixed(2);
            document.getElementById('load-5').textContent = systemInfo.loadAverage5.toFixed(2);
            document.getElementById('load-15').textContent = systemInfo.loadAverage15.toFixed(2);
          }

          // 更新进程数据
          if (processesRes.code === 200 && processesRes.data) {
            updateProcessesTable(processesRes.data);
          }
        }).catch(err => {
          console.error('获取实时数据失败:', err);
          cpuChart.hideLoading();
          memoryChart.hideLoading();
        });
      }

      // 更新CPU数据
      function updateCpuData(cpuInfo) {
        // 计算总使用率
        const totalUsage = 100 - cpuInfo.idle;

        // 更新CPU使用率卡片
        document.getElementById('cpu-usage').textContent = totalUsage.toFixed(1) + '%';
        document.getElementById('cpu-system').textContent = cpuInfo.system.toFixed(1) + '%';
        document.getElementById('cpu-user').textContent = cpuInfo.user.toFixed(1) + '%';

        // 更新CPU分布图表
        cpuChart.setOption({
          series: [{
            data: [
              { value: cpuInfo.user, name: '用户' },
              { value: cpuInfo.system, name: '系统' },
              { value: cpuInfo.nice, name: 'Nice' },
              { value: cpuInfo.wait, name: '等待' },
              { value: cpuInfo.hardwareIrq + cpuInfo.softwareIrq, name: '中断' },
              { value: cpuInfo.steal, name: 'Steal' },
              { value: cpuInfo.idle, name: '空闲' }
            ],
            itemStyle: {
              color: function(params) {
                const colorList = [
                  '#38bdf8', // 用户
                  '#3b82f6', // 系统
                  '#6366f1', // Nice
                  '#f59e0b', // 等待
                  '#ef4444', // 中断
                  '#8b5cf6', // Steal
                  '#94a3b8'  // 空闲
                ];
                return colorList[params.dataIndex];
              }
            }
          }]
        });
      }

      // 更新内存数据
      function updateMemoryData(memoryInfo) {
        // 计算内存使用率
        const memUsage = (memoryInfo.used / memoryInfo.total) * 100;

        // 更新内存卡片
        document.getElementById('mem-usage').textContent = memUsage.toFixed(1) + '%';
        document.getElementById('mem-used').textContent = memoryInfo.used.toFixed(1) + ' MB';
        document.getElementById('mem-available').textContent = memoryInfo.available.toFixed(1) + ' MB';

        // 更新交换空间卡片
        const swapUsage = (memoryInfo.swapUsed / memoryInfo.swapTotal) * 100;
        document.getElementById('swap-usage').textContent = swapUsage.toFixed(1) + '%';
        document.getElementById('swap-used').textContent = memoryInfo.swapUsed.toFixed(1) + ' MB';
        document.getElementById('swap-total').textContent = memoryInfo.swapTotal.toFixed(1) + ' MB';

        // 更新内存图表
        memoryChart.setOption({
          series: [{
            data: [
              memoryInfo.used,
              memoryInfo.free,
              memoryInfo.buffered,
              memoryInfo.available
            ]
          }]
        });
      }

      // 更新进程表格
      function updateProcessesTable(processes) {
        const tableBody = document.getElementById('processes-table-body');
        tableBody.innerHTML = '';

        if (processes.length === 0) {
          tableBody.innerHTML = `
          <tr>
          <td colspan="5" class="px-4 py-3 text-center text-gray-400">没有进程数据</td>
          </tr>
          `;
          return;
        }

        processes.forEach(process => {
          const tr = document.createElement('tr');
          tr.className = 'hover:bg-slate-700/50 transition-colors';
          tr.innerHTML = `
          <td class="px-4 py-3">${process.pid}</td>
          <td class="px-4 py-3">${process.user}</td>
          <td class="px-4 py-3 ${process.cpuUsage > 50 ? 'text-red-400' : process.cpuUsage > 20 ? 'text-yellow-400' : 'text-green-400'}">
          ${process.cpuUsage.toFixed(1)}%
          </td>
          <td class="px-4 py-3">${process.memoryUsage.toFixed(1)}%</td>
          <td class="px-4 py-3 truncate max-w-xs">${process.command}</td>
          `;
          tableBody.appendChild(tr);
        });
      }

      // 更新进程表格
      function updateProcessesTable(processes) {
        const tableBody = document.getElementById('processes-table-body');
        tableBody.innerHTML = '';

        if (!processes || processes.length === 0) {
          tableBody.innerHTML = `
          <tr>
          <td colspan="5" class="px-4 py-3 text-center text-gray-400">没有进程数据</td>
          </tr>
          `;
          return;
        }

        processes.forEach(process => {
          const tr = document.createElement('tr');
          tr.className = 'hover:bg-slate-700/50 transition-colors';
          tr.innerHTML = `
          <td class="px-4 py-3">${process.pid}</td>
          <td class="px-4 py-3">${process.user}</td>
          <td class="px-4 py-3 ${process.cpuUsage > 50 ? 'text-red-400' : process.cpuUsage > 20 ? 'text-yellow-400' : 'text-green-400'}">
          ${process.cpuUsage.toFixed(1)}%
          </td>
          <td class="px-4 py-3">${process.memoryUsage.toFixed(1)}%</td>
          <td class="px-4 py-3 truncate max-w-xs">${process.command}</td>
          `;
          tableBody.appendChild(tr);
        });
      }
    }
  };
}
      // 新增：为用户管理按钮添加点击事件
      if(f.id === 'user') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-white mb-4'>正在加载用户信息...</div>`;
          fetch('/api/user').then(res => res.json()).then(res => {
            if(res.code === 200 && res.data) {
              let html = `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                      `<div class='font-bold text-lg mb-2'>Linux用户列表</div>` +
                      `<table class='min-w-full text-sm'><thead><tr><th class='px-2 py-1'>用户名</th><th class='px-2 py-1'>UID</th><th class='px-2 py-1'>GID</th><th class='px-2 py-1'>描述</th><th class='px-2 py-1'>家目录</th><th class='px-2 py-1'>Shell</th></tr></thead><tbody>`;
              res.data.forEach(u => {
                html += `<tr><td class='px-2 py-1'>${u.username}</td><td class='px-2 py-1'>${u.uid}</td><td class='px-2 py-1'>${u.gid}</td><td class='px-2 py-1'>${u.comment}</td><td class='px-2 py-1'>${u.home}</td><td class='px-2 py-1'>${u.shell}</td></tr>`;
              });
              html += `</tbody></table></div>`;
              main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div>` + html;
            } else {
              main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-red-400'>用户信息获取失败：${res.message||'未知错误'}</div>`;
            }
          }).catch(err => {
            main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-red-400'>用户信息获取异常</div>`;
          });
        };
      }
      if (f.id === 'firewall') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-white mb-4'>正在加载防火墙规则...</div>`;
          fetch('/api/firewall')
                  .then(res => res.json())
                  .then(res => {
                    if (res.code === 200 && res.data) {
                      // 初始化内容变量
                      let content = ``;
                      // 遍历规则并添加到内容中
                      res.data.forEach(rule => {
                        content += `<div class='rule-item flex justify-between mb-2'>
              <div>${rule.comment}</div>
            </div>`;
                      });
                      // 添加添加规则按钮
                      content += `<button class='bento-btn mt-4' onclick='showAddFirewallRule()'>添加规则</button>`;
                      // 更新页面内容
                      main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div>` +
                              `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                              `<div class='font-bold text-lg mb-2'>防火墙规则列表</div>` + content + `</div>`;
                    } else {
                      // 错误处理
                      main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-red-400'>防火墙规则获取失败：${res.message || '未知错误'}</div>`;
                    }
                  })
                  .catch(err => {
                    // 异常处理
                    console.error('获取防火墙规则时发生异常', err);
                    main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-red-400'>防火墙规则获取异常：${err.message}</div>`;
                  });
        };
      }
      // 新增：为自动化运维按钮添加点击事件
      if(f.id === 'automation') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div>`+
                  `<div class='bento p-6 bg-slate-800 text-white mb-4'>`+
                  `<div class='font-bold text-lg mb-2'>自动化运维脚本上传与批量执行</div>`+
                  `<form id='upload-script-form' class='mb-4'>`+
                  `<input type='file' name='script' accept='.sh,.txt' class='bg-slate-900 rounded p-2 text-white mb-2' required />`+
                  `<button type='submit' class='bento-btn mt-2'>上传并批量执行</button>`+
                  `</form>`+
                  `<div id='automation-result' class='mt-4'></div>`+
                  `</div>`;
          document.getElementById('upload-script-form').onsubmit = function(e) {
            e.preventDefault();
            const form = e.target;
            const fileInput = form.querySelector('input[type=file]');
            if(!fileInput.files.length) return alert('请选择命令文本文件');
            const formData = new FormData();
            formData.append('script', fileInput.files[0]);
            fetch('/api/automation/upload', {method:'POST', body:formData}).then(res=>res.json()).then(res=>{
              if(Array.isArray(res.data)){
                document.getElementById('automation-result').innerHTML = res.data.map(r=>
                        `<div class='mb-2 p-2 rounded bg-slate-900'>`
                        +`<div class='font-bold text-green-400'>命令：${r.scriptName}</div>`
                        +`<div class='text-xs text-slate-400'>主机：${r.host} | 状态：${r.success?'✅成功':'❌失败'}</div>`
                        +`<pre class='mt-1 text-xs whitespace-pre-wrap'>${r.output}</pre>`
                        +`</div>`
                ).join('');
              }else{
                document.getElementById('automation-result').innerHTML = `<pre class='bg-slate-900 rounded p-2'>${res.data||res.message||'无返回结果'}</pre>`;
              }
            }).catch(()=>{
              document.getElementById('automation-result').innerHTML = `<span class='text-red-400'>上传或执行失败</span>`;
            });
          };
        };
      }
      // 新增：为进程管理按钮添加点击事件
      if(f.id === 'process') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>进程管理<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>Process Management</span></div>` +
                  `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                  `<button class='bento-btn mb-4' id='refresh-process-btn'>刷新进程列表</button>` +
                  `<div id='process-list'></div>` +
                  `</div>`;
          function loadProcesses() {
            fetch('/api/process/list').then(res => res.json()).then(res => {
              if(res.code === 200 && Array.isArray(res.data)) {
                document.getElementById('process-list').innerHTML = `<table class='min-w-full text-sm'><thead><tr><th>PID</th><th>用户</th><th>命令</th><th>CPU%</th><th>内存%</th><th>启动时间</th><th>状态</th><th>操作</th></tr></thead><tbody>` +
                        res.data.map(p => `<tr><td>${p.pid}</td><td>${p.user}</td><td>${p.command}</td><td>${p.cpu}</td><td>${p.mem}</td><td>${p.startTime}</td><td>${p.status}</td><td><button class='bento-btn-sm text-red-400' onclick='killProc(${p.pid})'>终止</button></td></tr>`).join('') +
                        `</tbody></table>`;
              } else {
                document.getElementById('process-list').innerHTML = `<span class='text-red-400'>${res.message || '暂无进程'}</span>`;
              }
            });
          }
          loadProcesses();
          document.getElementById('refresh-process-btn').onclick = loadProcesses;
          window.killProc = function(pid) {
            if(!confirm('确定要终止该进程吗？')) return;
            fetch('/api/process/kill?pid=' + pid, {method:'POST'}).then(res=>res.json()).then(res=>{
              alert(res.msg||'操作完成');
              loadProcesses();
            });
          };
        };
      }
      // 新增：为服务管理按钮添加点击事件
      if (f.id === 'service') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>服务管理<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>Service Management</span></div>` +
                  `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                  `<button class='bento-btn mb-4' id='refreshServiceBtn'>刷新服务列表</button>` +
                  `<table id='serviceTable' class='table'><thead><tr><th>服务名</th><th>状态</th><th>描述</th><th>启动类型</th><th>PID</th><th>操作</th></tr></thead><tbody></tbody></table>` +
                  `</div>`;
          loadServiceList();
          document.getElementById('refreshServiceBtn').onclick = loadServiceList;
        };
      }
      // 新增：为计划任务按钮添加点击事件
      if (f.id === 'scheduler') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>计划任务<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>Scheduler</span></div>` +
                  `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                  `<button class='bento-btn mb-4' id='add-task-btn'>新增任务</button>` +
                  `<div id='task-list'></div>` +
                  `</div>`;
          function loadTasks() {
            fetch('/api/task').then(res => res.json()).then(res => {
              if (res.code === 200 && Array.isArray(res.data)) {
                document.getElementById('task-list').innerHTML = res.data.map(t =>
                        `<div class='mb-2 p-2 rounded bg-slate-900 flex justify-between items-center'>` +
                        `<div><span class='font-bold'>${t.name}</span> <span class='ml-2 text-xs text-slate-400'>${t.cron}</span> <span class='ml-2 text-xs text-slate-400'>${t.command}</span> </div>` +
                        `<div><button class='bento-btn-sm mr-2' onclick='deleteTask("${t.id}")'>删除</button></div>` +
                        `</div>`
                ).join('');
              } else {
                document.getElementById('task-list').innerHTML = `<span class='text-red-400'>${res.message || '暂无任务'}</span>`;
              }
            });
          }
          loadTasks();
          document.getElementById('add-task-btn').onclick = function () {
            main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>新增计划任务</div>` +
                    `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                    `<form id='add-task-form' class='mb-4 flex flex-wrap gap-2'>` +
                    `<input name='name' placeholder='任务名称' class='bg-slate-900 rounded p-2 text-white' required />` +
                    `<input name='cron' placeholder='cron表达式' class='bg-slate-900 rounded p-2 text-white' required />` +
                    `<input name='command' placeholder='执行命令' class='bg-slate-900 rounded p-2 text-white' required />` +
                    `<button type='submit' class='bento-btn'>保存</button>` +
                    `</form>` +
                    `</div>`;
            document.getElementById('add-task-form').onsubmit = function (e) {
              e.preventDefault();
              const form = e.target;
              const data = { name: form.name.value, cron: form.cron.value, command: form.command.value, id: Date.now().toString() };
              fetch('/api/task', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(data) })
                      .then(res => res.json()).then(res => {
                alert(res.message || '添加成功');
                card.onclick();
              });
            };
          };
          window.deleteTask = function (id) {
            if (!confirm('确定要删除该任务吗？')) return;
            fetch('/api/task/' + id, { method: 'DELETE' })
                    .then(res => res.json()).then(res => {
              alert(res.message || '删除成功');
              loadTasks();
            });
          };
        };
      }
      if(f.id === 'inspection') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>巡检报告<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>Inspection Report</span></div>` +
                  `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                  `<button class='bento-btn mb-4' id='generate-report-btn'>生成实时巡检报告</button>` +
                  `<div id='report-list'></div>` +
                  `</div>`;

          // 加载报告列表
          function loadReports() {
            fetch('/api/report')
                    .then(res => res.json())
                    .then(res => {
                      if(res.code === 200 && Array.isArray(res.data)) {
                        document.getElementById('report-list').innerHTML = res.data.map(r =>
                                `<div class='mb-3 p-3 rounded bg-slate-900 flex flex-col md:flex-row justify-between items-start md:items-center gap-2'>` +
                                `<div>` +
                                `<span class='font-bold'>${r.title}</span>` +
                                `<div class='text-xs text-slate-400 mt-1'>` +
                                `生成时间: ${r.generateTime}` +
                                `</div>` +
                                `</div>` +
                                `<div class='flex gap-2'>` +
                                `<button class='bento-btn-sm' onclick='viewReport("${r.reportId}")'>查看详情</button>` +
                                (r.pdfUrl ? `<a class='bento-btn-sm' href='${r.pdfUrl}' target='_blank' download>下载报告</a>` :
                                        `<span class='text-slate-500 text-sm'>无下载文件</span>`) +
                                `</div>` +
                                `</div>`
                        ).join('');
                      } else {
                        document.getElementById('report-list').innerHTML = `<span class='text-red-400'>${res.message || '暂无报告数据'}</span>`;
                      }
                    })
                    .catch(err => {
                      document.getElementById('report-list').innerHTML = `<span class='text-red-400'>加载失败，请重试</span>`;
                    });
          }

          // 初始加载报告列表
          loadReports();

          // 生成报告按钮点击事件
          document.getElementById('generate-report-btn').onclick = function() {
            fetch('/api/report/generate', { method: 'POST' })
                    .then(res => res.json())
                    .then(res => {
                      if(res.code === 200) {
                        alert('报告生成成功');
                        loadReports(); // 重新加载列表
                      } else {
                        alert(res.message || '生成失败，请重试');
                      }
                    })
                    .catch(err => {
                      alert('生成请求失败，请检查网络');
                    });
          };

          // 查看报告详情函数
          window.viewReport = function(reportId) {
            fetch(`/api/report/${reportId}`)
                    .then(res => res.json())
                    .then(res => {
                      if(res.code === 200 && res.data) {
                        const report = res.data;
                        // 解析JSON内容为可读格式
                        let contentHtml = '';
                        try {
                          const contentObj = JSON.parse(report.content);
                          contentHtml = `
                <div class='space-y-4 mb-4'>
                  <div class='p-3 bg-slate-900 rounded'>
                    <div class='text-sm text-slate-400 mb-1'>系统状态概述</div>
                    <div class='font-medium'>${contentObj.summary}</div>
                  </div>

                  <div class='grid grid-cols-1 md:grid-cols-3 gap-3 mb-4'>
                    <div class='p-3 bg-slate-900 rounded'>
                      <div class='text-sm text-slate-400 mb-1'>CPU使用率</div>
                      <div class='flex items-center'>
                        <span class='font-medium mr-2'>${contentObj.cpuUsage}%</span>
                        <div class='w-full bg-slate-700 rounded-full h-2'>
                          <div class='bg-green-500 h-2 rounded-full' style='width: ${contentObj.cpuUsage}%'></div>
                        </div>
                      </div>
                    </div>

                    <div class='p-3 bg-slate-900 rounded'>
                      <div class='text-sm text-slate-400 mb-1'>内存使用率</div>
                      <div class='flex items-center'>
                        <span class='font-medium mr-2'>${contentObj.memoryUsage}%</span>
                        <div class='w-full bg-slate-700 rounded-full h-2'>
                          <div class='bg-blue-500 h-2 rounded-full' style='width: ${contentObj.memoryUsage}%'></div>
                        </div>
                      </div>
                    </div>

                    <div class='p-3 bg-slate-900 rounded'>
                      <div class='text-sm text-slate-400 mb-1'>磁盘使用率</div>
                      <div class='flex items-center'>
                        <span class='font-medium mr-2'>${contentObj.diskUsage}%</span>
                        <div class='w-full bg-slate-700 rounded-full h-2'>
                          <div class='bg-purple-500 h-2 rounded-full' style='width: ${contentObj.diskUsage}%'></div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class='p-3 bg-slate-900 rounded'>
                    <div class='text-sm text-slate-400 mb-2'>系统资源趋势</div>
                    <div class='font-medium'>${contentObj.trend}</div>
                    <div class='mt-2 text-xs text-slate-400'>过去24小时资源使用趋势显示系统运行稳定</div>
                  </div>

                  <div class='grid grid-cols-1 md:grid-cols-2 gap-3 mb-4'>
                    <div class='p-3 bg-slate-900 rounded'>
                      <div class='text-sm text-slate-400 mb-1'>告警数量</div>
                      <div class='font-medium ${contentObj.alerts > 0 ? 'text-red-400' : 'text-green-400'}'>${contentObj.alerts}</div>
                    </div>

                    <div class='p-3 bg-slate-900 rounded'>
                      <div class='text-sm text-slate-400 mb-1'>事件数量</div>
                      <div class='font-medium'>${contentObj.events}</div>
                    </div>
                  </div>

                  <div class='p-3 bg-slate-900 rounded'>
                    <div class='text-sm text-slate-400 mb-2'>系统负载</div>
                    <div class='flex justify-between'>
                      <div>
                        <div class='text-xs text-slate-400'>平均负载 (1/5/15分钟)</div>
                        <div class='font-medium'>${contentObj.loadAvg.join(' / ')}</div>
                      </div>
                      <div>
                        <div class='text-xs text-slate-400'>在线用户数</div>
                        <div class='font-medium'>${contentObj.userCount}</div>
                      </div>
                    </div>
                  </div>
                </div>
              `;
                        } catch (e) {
                          contentHtml = `<div class='p-3 bg-slate-900 rounded'>${report.content}</div>`;
                        }

                        main.innerHTML = `
              <div class='big-title mb-6 drop-shadow-lg'>巡检报告详情</div>
              <div class='bento p-6 bg-slate-800 text-white mb-4'>
                <div class='font-bold text-lg mb-3'>${report.title}</div>
                <div class='text-sm text-slate-400 mb-4'>生成时间: ${report.generateTime}</div>
                <div class='mb-6'>
                  <div class='font-medium mb-2'>报告内容</div>
                  ${contentHtml}
                </div>
                <div class='flex flex-wrap gap-3'>
                  ${report.pdfUrl ? `<a class='bento-btn' href='${report.pdfUrl}' target='_blank' download>下载PDF报告</a>` : ''}
                  <button class='bento-btn' onclick='loadInspectionPage()'>返回列表</button>
                </div>
              </div>
            `;
                      } else {
                        alert(res.message || '获取报告详情失败');
                      }
                    })
                    .catch(err => {
                      alert('获取详情失败，请重试');
                    });
          };

          // 返回列表的辅助函数
          window.loadInspectionPage = function() {
            card.click();
          };
        };
      }

      // 新增：为端口扫描按钮添加点击事件
      if(f.id === 'portscan') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>端口扫描<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>Port Scan</span></div>` +
                  `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                  `<button id='scan-btn' class='bento-btn mb-4'>扫描本机所有端口</button>` +
                  `<div id='scan-result'></div>` +
                  `</div>`;
          document.getElementById('scan-btn').onclick = function() {
            document.getElementById('scan-result').innerHTML = '正在扫描...';
            fetch('/api/port/scan', { method: 'POST' })
                    .then(res => res.json()).then(res => {
              if(res.code === 200 && Array.isArray(res.data)) {
                document.getElementById('scan-result').innerHTML = res.data.map(r =>
                        `<div class='mb-2 p-2 rounded bg-slate-900'>端口${r.port}：<span class='text-green-400'>${r.status}</span> <span class='ml-2 text-xs text-slate-400'>${r.processInfo||''}</span></div>`
                ).join('');
              } else {
                document.getElementById('scan-result').innerHTML = `<span class='text-red-400'>${res.message || '扫描失败'}</span>`;
              }
            });
          };
        };
      }
      // 新增：为端口检测按钮添加点击事件
      if(f.id === 'portcheck') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>端口检测<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>Port Check</span></div>` +
                  `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                  `<form id='check-form' class='mb-4 flex flex-wrap gap-2'>` +
                  `<input name='port' placeholder='端口号' class='bg-slate-900 rounded p-2 text-white' required />` +
                  `<button type='submit' class='bento-btn'>检测</button>` +
                  `</form>` +
                  `<div id='check-result'></div>` +
                  `</div>`;
          document.getElementById('check-form').onsubmit = function(e) {
            e.preventDefault();
            const form = e.target;
            const port = form.port.value;
            fetch('/api/port/check', { method: 'POST', headers: { 'Content-Type': 'application/x-www-form-urlencoded' }, body: `port=${encodeURIComponent(port)}` })
                    .then(res => res.json()).then(res => {
              if(res.code === 200 && res.data) {
                const r = res.data;
                document.getElementById('check-result').innerHTML = `<div class='mb-2 p-2 rounded bg-slate-900'>端口${r.port}：<span class='${r.status === "open" ? "text-green-400" : r.status === "closed" ? "text-red-400" : "text-yellow-400"}'>${r.status}</span> <span class='ml-2 text-xs text-slate-400'>${r.processInfo||''}</span> <span class='ml-2 text-xs text-slate-400'>耗时${r.durationMs}ms</span></div>`;
              } else {
                document.getElementById('check-result').innerHTML = `<span class='text-red-400'>${res.message || '检测失败'}</span>`;
              }
            });
          };
        };
      }
      // 新增：为安全加固按钮添加点击事件
// 点击后依次请求后端四个加固接口并展示检测结果，实现前后端联动。
      if(f.id === 'hardening') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-white mb-4'>正在检测安全加固项...</div>`;
          Promise.all([
            fetch('/api/hardening/checkWeakPassword').then(res => res.json()),
            fetch('/api/hardening/checkSystemPatch').then(res => res.json()),
            fetch('/api/hardening/checkOpenPorts').then(res => res.json()),
            fetch('/api/hardening/checkOtherItems').then(res => res.json())
          ]).then(results => {
            let html = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div>`;
            results.forEach((res, idx) => {
              if(res.code === 200 && res.data && res.data.length > 0) {
                res.data.forEach(item => {
                  html += `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                          `<div class='font-bold text-lg mb-2'>${item.item}</div>` +
                          `<div class='mb-1'><span class='font-bold'>状态：</span><span>${item.status}</span></div>` +
                          `<div class='mb-1'><span class='font-bold'>建议：</span><span>${item.suggestion}</span></div>` +
                          `<div class='mb-1'><span class='font-bold'>详情：</span><span>${item.detail}</span></div>` +
                          `</div>`;
                });
              } else {
                html += `<div class='bento p-6 bg-slate-800 text-white mb-4'>检测失败：${res.message||'未知错误'}</div>`;
              }
            });
            main.innerHTML = html;
          }).catch(err => {
            main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>${category.name}<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>${category.en}</span></div><div class='text-red-400'>安全加固检测异常</div>`;
          });
        };
      }
// 新增：为本地hosts管理按钮添加点击事件
      if (f.id === 'hosts') {
        card.style.cursor = 'pointer';
        card.onclick = () => {
          main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>本地hosts管理<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>Hosts Management</span></div>` +
                  `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
                  `<form id='hosts-form' class='mb-4 flex flex-wrap gap-2'>` +
                  `<input name='ip' placeholder='IP地址' class='bg-slate-900 rounded p-2 text-white' required />` +
                  `<input name='hostname' placeholder='主机名' class='bg-slate-900 rounded p-2 text-white' required />` +
                  `<button type='submit' class='bento-btn'>添加</button>` +
                  `</form>` +
                  `<div id='hosts-list'></div>` +
                  `</div>`;
          loadHosts();
          document.getElementById('hosts-form').onsubmit = function(e) {
            e.preventDefault();
            const form = e.target;
            const ip = form.ip.value;
            const hostname = form.hostname.value;
            fetch('/api/hosts/add', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ ip, hostname }) })
                    .then(res => res.json()).then(res => {
              alert(res.message || '添加成功');
              loadHosts();
            });
          };
        };
      }

      function loadHosts() {
        fetch('/api/hosts/list').then(res => res.json()).then(res => {
          if (res.code === 200 && Array.isArray(res.data)) {
            document.getElementById('hosts-list').innerHTML = res.data.map((h, i) =>
                    `<div class='mb-2 p-2 rounded bg-slate-900 flex justify-between items-center'>` +
                    `<div><span class='font-bold'>${h.ip}</span> <span class='ml-2'>${h.hostname}</span></div>` +
                    `<div><button class='bento-btn-sm' onclick='deleteHost(${i})'>删除</button></div>` +
                    `</div>`
            ).join('');
          } else {
            document.getElementById('hosts-list').innerHTML = `<span class='text-red-400'>${res.message || '暂无数据'}</span>`;
          }
        });
      }


      window.deleteHost = function(idx) {
        fetch('/api/hosts/list').then(res => res.json()).then(res => {
          if (res.code === 200 && Array.isArray(res.data)) {
            const h = res.data[idx];
            // 修改后的请求，直接传递 ip 和 hostname
            fetch('/api/hosts/delete', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ ip: h.ip, hostname: h.hostname }) })
                    .then(res => res.json()).then(res => {
              alert(res.message || '删除成功');
              loadHosts();
            });
          }
        });
      };

      grid.appendChild(card);
    });
    main.appendChild(grid);
  }

  // 页面加载后渲染导航
  window.onload = () => {
    renderCategories(categories[0]?.id);
    renderFeatures(categories[0]);
  };

  // 滚动动效
  window.addEventListener('scroll', () => {
    document.querySelectorAll('.scroll-animate').forEach(el => {
      const rect = el.getBoundingClientRect();
      if(rect.top < window.innerHeight) el.style.transform = 'translateY(0)';
      else el.style.transform = 'translateY(60px)';
    });
  });


  // 显示添加规则表单
  window.showAddFirewallRule = function() {
    const main = document.getElementById('main-content');
    main.innerHTML = `<div class='big-title mb-6 drop-shadow-lg'>防火墙配置<span class='en-title ml-4' style='color:#94a3b8;text-shadow:none;'>Firewall</span></div>` +
            `<div class='bento p-6 bg-slate-800 text-white mb-4'>` +
            `<div class='font-bold text-lg mb-2'>添加防火墙规则</div>` +
            `<form id='add-fw-form' class='grid grid-cols-2 gap-4'>` +
            `<input name='id' placeholder='规则ID' class='bg-slate-900 rounded p-2 text-white' required />` +
            `<input name='name' placeholder='名称' class='bg-slate-900 rounded p-2 text-white' required />` +
            `<input name='protocol' placeholder='协议(TCP/UDP/ALL)' class='bg-slate-900 rounded p-2 text-white' required />` +
            `<input name='port' placeholder='端口' class='bg-slate-900 rounded p-2 text-white' required />` +
            `<input name='sourceIp' placeholder='来源IP' class='bg-slate-900 rounded p-2 text-white' required />` +
            `<input name='destIp' placeholder='目标IP' class='bg-slate-900 rounded p-2 text-white' required />` +
            `<input name='action' placeholder='动作(ACCEPT/DROP/REJECT)' class='bg-slate-900 rounded p-2 text-white' required />` +
            `<input name='comment' placeholder='备注' class='bg-slate-900 rounded p-2 text-white' />` +
            `<button type='submit' class='bento-btn col-span-2 mt-4'>提交</button>` +
            `</form></div>`;
    document.getElementById('add-fw-form').onsubmit = function(e) {
      e.preventDefault();
      const form = e.target;
      const data = {};
      for(const el of form.elements) if(el.name) data[el.name] = el.value;
      fetch('/api/firewall', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(data) }).then(res => res.json()).then(res => {
        alert(res.message || '操作完成');
        location.reload();
      });
    }
  }
  // 新增：服务管理功能实现
  function loadServiceList() {
    fetch('/api/service/list')
            .then(res => {
              if (!res.ok) {
                throw new Error('网络响应错误，状态码: ' + res.status);
              }
              return res.json();
            })
            .then(data => {
              if (data.code === 200 && Array.isArray(data.data)) {
                const tbody = document.querySelector('#serviceTable tbody');
                tbody.innerHTML = '';
                data.data.forEach(service => {
                  const tr = document.createElement('tr');
                  tr.innerHTML = `
                        <td>${service.name}</td>
                        <td>${service.status}</td>
                        <td>${service.description}</td>
                        <td>${service.startType}</td>
                        <td>${service.pid}</td>
                        <td>
                            <button class="bento-btn operation-btn" onclick="operateService('${service.name}','start')">启动</button>
                            <button class="bento-btn operation-btn" onclick="operateService('${service.name}','stop')">停止</button>
                            <button class="bento-btn operation-btn" onclick="operateService('${service.name}','restart')">重启</button>
                        </td>
                    `;
                  tbody.appendChild(tr);
                });
              } else {
                console.error('返回数据格式错误:', data); // 打印错误信息
                alert('加载服务列表失败：' + (data.message || '未知错误'));
              }
            })
            .catch(err => {
              console.error('加载服务列表时发生错误', err);
              alert('加载服务列表失败，请稍后重试');
            });
  }

  // 操作服务（启动、停止、重启）
  function operateService(name, action) {
    fetch(`/api/service/${action}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ name }) // 确保传递 name 参数
    })
            .then(res => {
              if (!res.ok) {
                throw new Error('网络响应错误，状态码: ' + res.status);
              }
              return res.json();
            })
            .then(data => {
              if (data.code === 200) {
                alert(`${action === 'start' ? '启动' : action === 'stop' ? '停止' : '重启'}服务成功: ${data.msg}`);
                loadServiceList(); // 刷新服务列表
              } else {
                alert(`${action === 'start' ? '启动' : action === 'stop' ? '停止' : '重启'}服务失败：${data.msg}`);
              }
            })
            .catch(err => {
              console.error('操作服务时发生错误', err);
              alert('操作服务失败，请稍后重试');
            });
  }

</script>
</body>
</html>




