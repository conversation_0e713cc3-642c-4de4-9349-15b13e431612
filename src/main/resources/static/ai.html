<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>麒麟智能运维系统 - AI模块</title>
  <!-- 外部资源CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <!-- 统一数据文件 -->
  <script src="/data/data.js"></script>
  <link rel="Shortcut Icon" href="./favicon.ico" type="image/x-icon" />
  <style>
    /* 继承主系统的样式 */
    .table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }

    .table th,
    .table td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #333;
    }

    .table th {
      background-color: #2e3440;
      color: #fff;
    }

    .table tr:hover {
      background-color: #2e3e50;
    }

    .table td {
      padding: 8px 12px;
    }

    .table .operation-btn {
      background-color: transparent;
      border: 1px solid #3b82f6;
      color: black;
      border-radius: 4px;
      padding: 4px 8px;
      margin-right: 5px;
      cursor: pointer;
      transition: all 0.3s;
    }

    .table .operation-btn:hover {
      background-color: #3b82f6;
      color: #fff;
    }

    .table .operation-btn:active {
      background-color: #2563eb;
    }
    .rule-item {
      font-size: 1rem;
      color: #e2e8f0;
      margin-bottom: 0.5rem;
    }
    body { background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%); }
    .bento { box-shadow: 0 4px 32px 0 rgba(0,0,0,0.15); border-radius: 1.5rem; }
    .bento:hover { box-shadow: 0 8px 48px 0 rgba(0,0,0,0.25); }
    .highlight { background: linear-gradient(90deg,rgba(56,189,248,0.7),rgba(59,130,246,0.3)); }
    .big-title { font-size: 2.8rem; font-weight: bold; letter-spacing: 2px; color: #f1f5f9; text-shadow: 0 2px 12px rgba(16, 185, 255, 0.25), 0 1px 0 #0ea5e9; background: linear-gradient(90deg, #38bdf8 10%, #6366f1 90%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
    .en-title { font-size: 1rem; color: #94a3b8; }
    .bento-img { object-fit: cover; border-radius: 1rem; }
    .bento-icon { font-size: 2.5rem; color: #38bdf8; }
    .bento-btn {
      transition: transform 0.2s, box-shadow 0.2s, background 0.3s;
      background: linear-gradient(120deg, rgba(56,189,248,0.85) 0%, rgba(59,130,246,0.45) 100%);
      box-shadow: 0 2px 16px 0 rgba(56,189,248,0.15), 0 1.5px 8px 0 rgba(59,130,246,0.10);
      border: 2px solid rgba(56,189,248,0.25);
      color: #fff;
      font-weight: bold;
      letter-spacing: 1px;
      font-size: 1.25rem;
      padding: 1.1rem 1.5rem;
      border-radius: 1.2rem;
      position: relative;
      overflow: hidden;
    }
    .bento-btn::before {
      content: '';
      position: absolute;
      left: 0; top: 0; right: 0; bottom: 0;
      background: linear-gradient(90deg,rgba(255,255,255,0.08) 0%,rgba(56,189,248,0.12) 100%);
      opacity: 0.7;
      z-index: 0;
      border-radius: 1.2rem;
      pointer-events: none;
      transition: opacity 0.3s;
    }
    .bento-btn:hover {
      transform: scale(1.08) translateY(-2px);
      box-shadow: 0 8px 32px 0 rgba(56,189,248,0.25), 0 2px 16px 0 rgba(59,130,246,0.18);
      background: linear-gradient(120deg, rgba(59,130,246,0.95) 0%, rgba(56,189,248,0.7) 100%);
      border-color: #38bdf8;
    }
    .bento-btn:active {
      transform: scale(0.97);
      box-shadow: 0 2px 8px 0 rgba(56,189,248,0.10);
    }
    .bento-btn > * {
      position: relative;
      z-index: 1;
    }
    .scroll-animate { transition: all 0.7s cubic-bezier(.4,0,.2,1); }

    /* 聊天对话框样式 - 采用第一个代码的样式 */
    .chat-container {
      display: flex;
      flex-direction: column;
      height: 75vh;
      background-color: rgba(30, 41, 59, 0.7);
      border-radius: 1.5rem;
      overflow: hidden;
      box-shadow: 0 8px 48px 0 rgba(0,0,0,0.25);
    }

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      padding: 1.5rem;
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .chat-input-container {
      padding: 1rem;
      border-top: 1px solid rgba(148, 163, 184, 0.2);
      background-color: rgba(15, 23, 42, 0.5);
    }

    .chat-input-wrapper {
      display: flex;
      gap: 0.75rem;
      align-items: center;
    }

    .chat-input {
      flex: 1;
      background-color: rgba(59, 130, 246, 0.1);
      border: 1px solid rgba(56, 189, 248, 0.3);
      border-radius: 1rem;
      padding: 0.75rem 1.25rem;
      color: #e2e8f0;
      font-size: 1rem;
      outline: none;
      transition: border-color 0.3s;
    }

    .chat-input:focus {
      border-color: rgba(56, 189, 248, 0.8);
    }

    .send-button {
      background: linear-gradient(120deg, rgba(56,189,248,0.85) 0%, rgba(59,130,246,0.85) 100%);
      border: none;
      border-radius: 1rem;
      padding: 0.75rem 1.5rem;
      color: white;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s;
    }

    .send-button:hover {
      background: linear-gradient(120deg, rgba(59,130,246,0.95) 0%, rgba(56,189,248,0.95) 100%);
      transform: scale(1.05);
    }

    .send-button:active {
      transform: scale(0.95);
    }

    .user-message, .ai-message {
      display: flex;
      gap: 1rem;
      max-width: 90%;
    }

    .user-message {
      align-self: flex-end;
      flex-direction: row-reverse;
    }

    .ai-message {
      align-self: flex-start;
    }

    .message-content {
      padding: 0.75rem 1.25rem;
      border-radius: 1rem;
      position: relative;
    }

    .user-message .message-content {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(79, 70, 229, 0.8) 100%);
      color: white;
    }

    .ai-message .message-content {
      background: linear-gradient(135deg, rgba(15, 23, 42, 0.7) 0%, rgba(30, 41, 59, 0.7) 100%);
      color: #e2e8f0;
      border: 1px solid rgba(56, 189, 248, 0.2);
    }

    .message-avatar {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.25rem;
      flex-shrink: 0;
    }

    .user-message .message-avatar {
      background-color: rgba(59, 130, 246, 0.8);
      color: white;
    }

    .ai-message .message-avatar {
      background-color: rgba(56, 189, 248, 0.8);
      color: #0f172a;
    }

    /* 加载动画 */
    .typing-indicator {
      display: flex;
      gap: 0.25rem;
      padding: 0.75rem 0;
    }

    .typing-dot {
      width: 0.5rem;
      height: 0.5rem;
      background-color: rgba(56, 189, 248, 0.8);
      border-radius: 50%;
      animation: typing-animation 1.4s infinite ease-in-out;
    }

    .typing-dot:nth-child(2) {
      animation-delay: 0.2s;
    }

    .typing-dot:nth-child(3) {
      animation-delay: 0.4s;
    }

    @keyframes typing-animation {
      0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.6;
      }
      30% {
        transform: translateY(-0.3rem);
        opacity: 1;
      }
    }

    /* 链接样式 */
    .search-result a {
      color: #38bdf8;
      text-decoration: none;
      transition: color 0.3s;
    }

    .search-result a:hover {
      color: #60a5fa;
      text-decoration: underline;
    }

    /* 搜索结果样式 */
    .search-result {
      margin-top: 0.75rem;
      padding-top: 0.75rem;
      border-top: 1px solid rgba(148, 163, 184, 0.2);
      font-size: 0.9rem;
      color: #94a3b8;
    }

    /* 滚动条美化 */
    .chat-messages::-webkit-scrollbar {
      width: 0.5rem;
    }

    .chat-messages::-webkit-scrollbar-track {
      background: rgba(15, 23, 42, 0.5);
      border-radius: 0.5rem;
    }

    .chat-messages::-webkit-scrollbar-thumb {
      background: rgba(59, 130, 246, 0.3);
      border-radius: 0.5rem;
    }

    .chat-messages::-webkit-scrollbar-thumb:hover {
      background: rgba(59, 130, 246, 0.5);
    }

    /* 采用第一个代码的询问按钮样式 */
    .confirm-buttons {
      display: flex;
      gap: 0.75rem;
      margin-top: 0.75rem;
    }

    .operation-btn {
      background-color: transparent;
      border: 1px solid #3b82f6;
      color: #e2e8f0;
      border-radius: 4px;
      padding: 4px 8px;
      margin-right: 5px;
      cursor: pointer;
      transition: all 0.3s;
    }

    .operation-btn:hover {
      background-color: #3b82f6;
      color: #fff;
    }

    .operation-btn:active {
      background-color: #2563eb;
    }

    /* Markdown解析样式 */
    .markdown-content h1, .markdown-content h2, .markdown-content h3,
    .markdown-content h4, .markdown-content h5, .markdown-content h6 {
      font-weight: 600;
      line-height: 1.25;
      margin-bottom: 0.75rem;
      margin-top: 1.5rem;
    }

    .markdown-content h1, .markdown-content h2 {
      border-bottom: 1px solid rgba(148, 163, 184, 0.2);
      padding-bottom: 0.5rem;
    }

    .markdown-content p {
      margin-bottom: 1rem;
      line-height: 1.6;
    }

    .markdown-content ul, .markdown-content ol {
      margin-left: 1.5rem;
      margin-bottom: 1rem;
    }

    .markdown-content ul {
      list-style-type: disc;
    }

    .markdown-content ol {
      list-style-type: decimal;
    }

    .markdown-content li {
      margin-bottom: 0.5rem;
    }

    .markdown-content code {
      color: #60a5fa;
      padding: 0.2rem 0.4rem;
      background: rgba(59, 130, 246, 0.1);
      border-radius: 0.25rem;
      font-size: 0.9rem;
    }

    .markdown-content pre {
      background: rgba(15, 23, 42, 0.8);
      padding: 1rem;
      border-radius: 0.5rem;
      margin-bottom: 1rem;
      overflow-x: auto;
      color: #e2e8f0;
    }

    .markdown-content pre code {
      background: transparent;
      padding: 0;
      color: inherit;
    }

    .markdown-content a {
      color: #38bdf8;
      text-decoration: none;
      transition: color 0.3s;
    }

    .markdown-content a:hover {
      color: #60a5fa;
      text-decoration: underline;
    }

    .markdown-content blockquote {
      border-left: 4px solid rgba(56, 189, 248, 0.5);
      padding-left: 1rem;
      margin-left: 0;
      margin-bottom: 1rem;
      color: #94a3b8;
    }

    .markdown-content img {
      max-width: 100%;
      border-radius: 0.5rem;
      margin: 1rem 0;
    }
  </style>

</head>
<body class="min-h-screen flex flex-col">
<div class="flex flex-1">
  <!-- 左侧导航 - 与主系统保持一致 -->
  <aside class="w-64 bg-slate-900 text-white flex flex-col py-8 px-4 shadow-xl">
    <div class="mb-10">
      <span class="big-title" style="color:#fff;background:none;-webkit-text-fill-color:unset;text-shadow:none;">麒麟保安</span>
      <div class="en-title">Qilin BaoAn</div>
    </div>
    <nav id="category-nav" class="flex flex-col gap-2"></nav>
  </aside>
  <!-- 右侧内容区 -->
  <main class="flex-1 p-10 overflow-y-auto">
    <div id="main-content">
      <!-- AI模块标题 -->
      <div class="big-title mb-6 drop-shadow-lg">智能运维<span class="en-title ml-4" style="color:#94a3b8;text-shadow:none;">AI OP</span></div>

      <!-- 聊天对话框 - 使用第一个代码的样式 -->
      <div class="chat-container scroll-animate" style="transform: translateY(60px);">
        <div class="chat-messages" id="chatMessages">
          <!-- 初始消息 -->
          <div class="ai-message">
            <div class="message-avatar">
              <i class="fa fa-robot"></i>
            </div>
            <div class="message-content markdown-content">
              您好！我是麒麟智能运维助手，有什么可以帮助您解决的运维问题吗？
              <p class="search-result">我会基于知识库检索并通过联网搜索为您提供最新的解决方案，只需在提问中明确需要搜索的内容。</p>
            </div>
          </div>
        </div>
        <div class="chat-input-container">
          <div class="chat-input-wrapper">
            <input type="text" class="chat-input" id="userInput" placeholder="请输入您的问题...">
            <button class="send-button" id="sendButton">
              <i class="fa fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>
<footer class="text-center text-slate-400 py-4">© 麒麟保安智能运维系统 Qilin BaoAn</footer>
<script>
  // 添加AI模块到分类列表
  categories.push({
    id: 'ai',
    name: '智能运维',
    en: 'AI OP',
    icon: 'fa-robot'
  });

  // 动态渲染导航
  function renderCategories(selectedId) {
    const nav = document.getElementById('category-nav');
    nav.innerHTML = '';
    categories.forEach(cat => {
      const btn = document.createElement('button');
      btn.className = 'bento-btn flex flex-row items-center w-full h-14 px-4 py-2 highlight transition-all duration-200 text-left rounded-lg mb-1' + (selectedId === cat.id ? ' ring-2 ring-cyan-400 bg-gradient-to-r from-cyan-500/80 to-blue-500/60' : ' bg-slate-800/80 hover:bg-cyan-700/40');
      btn.innerHTML = `<i class="fa ${cat.icon} bento-icon mb-3" style="font-size:1.3rem;"></i><span class="font-bold text-base">${cat.name}</span><span class="en-title ml-2 text-xs">${cat.en}</span>`;
      btn.onclick = () => {
        window.location.href = cat.id === 'ai' ? '/ai.html' : `/index.html#${cat.id}`;
      };
      nav.appendChild(btn);
    });
  }

  // 滚动动效 - 来自第一个代码
  window.addEventListener('scroll', () => {
    document.querySelectorAll('.scroll-animate').forEach(el => {
      const rect = el.getBoundingClientRect();
      if(rect.top < window.innerHeight) el.style.transform = 'translateY(0)';
      else el.style.transform = 'translateY(60px)';
    });
  });

  // Coze API配置
  const COZE_API_URL = 'https://api.coze.cn/v1/workflow/stream_run';
  const COZE_RESUME_URL = 'https://api.coze.cn/v1/workflow/stream_resume';
  const COZE_API_KEY = 'cztei_lH1iFbCyQ5UTf5j5YhKdm7dc5bGzsTE7EWaAbsrNC9YSfd1h9A2CHJHYSraGAUQpN';
  const WORKFLOW_ID = '7524907604734558249';

  // 解析Coze API的非标准响应格式
  function parseCozeResponse(chunk) {
    // 尝试解析为JSON
    try {
      return JSON.parse(chunk);
    } catch (e) {
      // 解析类似 "key: value" 的格式
      const lines = chunk.split('\n').filter(line => line.trim() !== '');
      const parsedData = {};

      let currentKey = null;
      let currentValue = '';

      lines.forEach(line => {
        line = line.trim();
        if (!line) return;

        // 检查是否为新的键值对
        const colonIndex = line.indexOf(':');
        if (colonIndex > 0) {
          // 如果有正在处理的键值对，先保存
          if (currentKey) {
            parsedData[currentKey] = currentValue.trim();
          }

          // 开始新的键值对
          currentKey = line.substring(0, colonIndex).trim();
          currentValue = line.substring(colonIndex + 1).trim();
        } else {
          // 延续当前的值
          currentValue += '\n' + line;
        }
      });

      // 保存最后一个键值对
      if (currentKey) {
        parsedData[currentKey] = currentValue.trim();
      }

      // 尝试解析嵌套的JSON
      Object.keys(parsedData).forEach(key => {
        try {
          parsedData[key] = JSON.parse(parsedData[key]);
        } catch (e) {
          // 不是有效的JSON，保持原样
        }
      });

      return parsedData;
    }
  }

  // 处理中断消息 - 合并重复逻辑
  function handleInterruptMessage(contentDiv, nodeTitle, newEventId, content) {
    // 清空旧内容，渲染新交互提示
    contentDiv.innerHTML = renderMarkdown(content);

    // 仅在“问答”节点显示操作按钮
    if (nodeTitle === '问答') {
      // 查找或创建搜索结果容器
      let searchResult = contentDiv.querySelector('.search-result');

      if (!searchResult) {
        // 创建搜索结果容器
        searchResult = document.createElement('div');
        searchResult.className = 'search-result';
        searchResult.innerHTML = "以上信息是基于知识库和联网搜索通过工作流汇总的结果";
        contentDiv.appendChild(searchResult);
      }

      // 创建操作按钮容器
      const actionsDiv = document.createElement('div');
      actionsDiv.className = 'flex space-x-2 mt-3';

      // 运行按钮
      const runButton = document.createElement('button');
      runButton.className = 'px-3 py-1 bg-gradient-to-r from-cyan-500 to-blue-500 text-white rounded-md text-sm font-medium transition-all hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-cyan-400';
      runButton.innerHTML = '<i class="fa fa-play-circle mr-1"></i> 运行';
      runButton.addEventListener('click', () => resumeWorkflow('运行', newEventId, contentDiv));

      // 不运行按钮
      const cancelButton = document.createElement('button');
      cancelButton.className = 'px-3 py-1 bg-gray-700 text-white rounded-md text-sm font-medium transition-all hover:bg-gray-600 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-gray-500';
      cancelButton.innerHTML = '<i class="fa fa-times-circle mr-1"></i> 不运行';
      cancelButton.addEventListener('click', () => resumeWorkflow('不运行', newEventId, contentDiv));

      // 组装DOM - 按钮放在搜索结果文本下方
      actionsDiv.append(runButton, cancelButton);
      searchResult.appendChild(actionsDiv);
    }
  }

  // 发送消息到Coze工作流
  async function sendToCoze(message) {
    const chatMessages = document.getElementById('chatMessages');

    // 创建AI消息元素
    const aiMessageDiv = document.createElement('div');
    aiMessageDiv.className = 'ai-message';
    aiMessageDiv.innerHTML = `
      <div class="message-avatar">
        <i class="fa fa-robot"></i>
      </div>
      <div class="message-content markdown-content">
        <div class="typing-indicator">
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
        </div>
      </div>
    `;
    chatMessages.appendChild(aiMessageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    try {
      // 构建新工作流请求体
      const requestBody = {
        workflow_id: WORKFLOW_ID,
        parameters: {
          USER_INPUT: message
        }
      };

      // 发送请求
      const response = await fetch(COZE_API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${COZE_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }

      // 处理流式响应
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let aiResponseText = '';
      let isFirstChunk = true;
      let currentEventData = {};

      const contentDiv = aiMessageDiv.querySelector('.message-content');

      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          // 添加搜索结果标记（如果之前没有添加过）
          if (aiResponseText && !contentDiv.querySelector('.search-result')) {
            const searchResult = document.createElement('div');
            searchResult.className = 'search-result';
            searchResult.innerHTML = "以上信息是基于知识库和联网搜索通过工作流汇总的结果";
            contentDiv.appendChild(searchResult);
          }
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n').filter(line => line.trim() !== '');

        for (const line of lines) {
          try {
            const data = parseCozeResponse(line);

            // 收集事件数据
            if (data.event) {
              currentEventData = { event: data.event };
            }

            // 合并其他字段
            Object.assign(currentEventData, data);

            // 处理完整事件
            if (currentEventData.event && currentEventData.data) {
              if (isFirstChunk) {
                contentDiv.innerHTML = '';
                isFirstChunk = false;
              }

              // 处理不同事件类型
              switch (currentEventData.event) {
                case 'Message':
                  aiResponseText += currentEventData.data.content || '';
                  contentDiv.innerHTML = renderMarkdown(aiResponseText);
                  break;

                case 'Interrupt':
                  // 解析交互内容
                  const interruptData = currentEventData.data.interrupt_data;
                  const newEventId = interruptData.event_id;
                  const nodeTitle = currentEventData.data.node_title;
                  let content = '';
                  try { content = JSON.parse(interruptData.data).content; }
                  catch (e) { content = interruptData.data; }

                  // 使用统一的中断处理函数
                  handleInterruptMessage(contentDiv, nodeTitle, newEventId, content);
                  break;

                case 'Error':
                  contentDiv.innerHTML = `发生错误: ${currentEventData.msg || '未知错误'}`;
                  break;
              }

              // 重置事件数据
              currentEventData = {};
            }
          } catch (e) {
            console.error('解析响应失败:', e);
            console.error('有问题的行:', line);
          }
        }

        chatMessages.scrollTop = chatMessages.scrollHeight;
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      const contentDiv = aiMessageDiv.querySelector('.message-content');
      contentDiv.innerHTML = `抱歉，无法连接到AI服务: ${error.message}`;
    }
  }

  // 恢复工作流
  async function resumeWorkflow(option, eventId, messageContent) {
    // 移除操作按钮并显示用户选择
    const searchResult = messageContent.querySelector('.search-result');
    const actionsDiv = searchResult.querySelector('.flex');
    if (actionsDiv) {
      actionsDiv.remove();
    }

    // 更新搜索结果区域显示用户选择
    const userChoiceDiv = document.createElement('div');
    userChoiceDiv.className = 'mt-2 flex items-center text-sm';

    // 根据选择类型设置不同的图标和颜色
    if (option === '不运行') {
      userChoiceDiv.innerHTML = `
            <i class="fa fa-times-circle text-red-500 mr-2"></i>
            <span>您选择了: <strong class="text-red-500">${option}</strong></span>
        `;
    } else {
      userChoiceDiv.innerHTML = `
            <i class="fa fa-check-circle text-green-400 mr-2"></i>
            <span>您选择了: <strong class="text-green-400">${option}</strong></span>
        `;
    }

    searchResult.appendChild(userChoiceDiv);

    // 滚动到底部
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // 创建AI消息元素（显示处理中）
    const aiMessageDiv = document.createElement('div');
    aiMessageDiv.className = 'ai-message';
    aiMessageDiv.innerHTML = `
      <div class="message-avatar">
        <i class="fa fa-robot"></i>
      </div>
      <div class="message-content markdown-content">
        <div class="typing-indicator">
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
        </div>
      </div>
    `;
    chatMessages.appendChild(aiMessageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;

    try {
      // 构建恢复工作流请求体
      const requestBody = {
        workflow_id: WORKFLOW_ID,
        event_id: eventId,
        interrupt_type: 2,
        resume_data: option
      };

      // 发送请求到恢复接口
      const response = await fetch(COZE_RESUME_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${COZE_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }

      // 处理流式响应
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let aiResponseText = '';
      let isFirstChunk = true;
      let currentEventData = {};

      const contentDiv = aiMessageDiv.querySelector('.message-content');

      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          // 添加搜索结果标记（如果之前没有添加过）
          if (aiResponseText && !contentDiv.querySelector('.search-result')) {
            const searchResult = document.createElement('div');
            searchResult.className = 'search-result';
            searchResult.innerHTML = "以上信息是基于知识库和联网搜索通过工作流汇总的结果";
            contentDiv.appendChild(searchResult);
          }
          break;
        }

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n').filter(line => line.trim() !== '');

        for (const line of lines) {
          try {
            const data = parseCozeResponse(line);

            // 收集事件数据
            if (data.event) {
              currentEventData = { event: data.event };
            }

            // 合并其他字段
            Object.assign(currentEventData, data);

            // 处理完整事件
            if (currentEventData.event && currentEventData.data) {
              if (isFirstChunk) {
                contentDiv.innerHTML = '';
                isFirstChunk = false;
              }

              // 处理不同事件类型
              switch (currentEventData.event) {
                case 'Message':
                  aiResponseText += currentEventData.data.content || '';
                  contentDiv.innerHTML = renderMarkdown(aiResponseText);
                  break;

                case 'Interrupt':
                  // 提取关键信息
                  const interruptData = currentEventData.data.interrupt_data;
                  const newEventId = interruptData.event_id;
                  const nodeTitle = currentEventData.data.node_title;

                  // 解析嵌套的content
                  let content = '';
                  try {
                    const nestedData = JSON.parse(interruptData.data);
                    content = nestedData.content || '';
                  } catch (e) {
                    content = interruptData.data || '';
                  }

                  // 使用统一的中断处理函数
                  handleInterruptMessage(contentDiv, nodeTitle, newEventId, content);
                  break;

                case 'Error':
                  contentDiv.innerHTML = `发生错误: ${currentEventData.msg || '未知错误'}`;
                  break;
              }

              // 重置事件数据
              currentEventData = {};
            }
          } catch (e) {
            console.error('解析响应失败:', e);
            console.error('有问题的行:', line);
          }
        }

        chatMessages.scrollTop = chatMessages.scrollHeight;
      }
    } catch (error) {
      console.error('恢复工作流失败:', error);
      const contentDiv = aiMessageDiv.querySelector('.message-content');
      contentDiv.innerHTML = `抱歉，无法恢复工作流: ${error.message}`;
    }
  }

  // 发送用户消息
  function sendMessage(message) {
    if (!message.trim()) return;

    const chatMessages = document.getElementById('chatMessages');
    const userInput = document.getElementById('userInput');

    // 创建用户消息元素
    const userMessageDiv = document.createElement('div');
    userMessageDiv.className = 'user-message';
    userMessageDiv.innerHTML = `
      <div class="message-avatar">
        <i class="fa fa-user"></i>
      </div>
      <div class="message-content markdown-content">${renderMarkdown(message)}</div>
    `;
    chatMessages.appendChild(userMessageDiv);

    // 清空输入框并滚动到底部
    userInput.value = '';
    chatMessages.scrollTop = chatMessages.scrollHeight;

    // 发送到Coze
    sendToCoze(message);
  }

  // Markdown解析函数
  function renderMarkdown(markdown) {
    // 处理换行符
    markdown = markdown.replace(/\n/g, '<br>');

    // 处理标题
    markdown = markdown.replace(/^(#+)\s+(.*?)(<br>)?$/gm, function(match, hashes, content) {
      const level = hashes.length;
      return `<h${level}>${content}</h${level}>`;
    });

    // 处理加粗
    markdown = markdown.replace(/\*\*(.*?)\*\*/gm, '<strong>$1</strong>');
    markdown = markdown.replace(/__(.*?)__/gm, '<strong>$1</strong>');

    // 处理斜体
    markdown = markdown.replace(/\*(.*?)\*/gm, '<em>$1</em>');
    markdown = markdown.replace(/_(.*?)_/gm, '<em>$1</em>');

    // 处理删除线
    markdown = markdown.replace(/~~(.*?)~~/gm, '<del>$1</del>');

    // 处理链接
    markdown = markdown.replace(/\[(.*?)\]\((.*?)\)/gm, '<a href="$2" target="_blank">$1</a>');

    // 处理图片
    markdown = markdown.replace(/!\[(.*?)\]\((.*?)\)/gm, '<img src="$2" alt="$1" />');

    // 处理代码块
    markdown = markdown.replace(/```([\s\S]*?)```/gm, '<pre><code>$1</code></pre>');

    // 处理行内代码
    markdown = markdown.replace(/`(.*?)`/gm, '<code>$1</code>');

    // 处理引用
    markdown = markdown.replace(/^> (.*)$/gm, '<blockquote>$1</blockquote>');

    // 处理无序列表
    markdown = markdown.replace(/^\* (.*)$/gm, '<li>$1</li>');
    markdown = markdown.replace(/^- (.*)$/gm, '<li>$1</li>');

    // 处理有序列表
    markdown = markdown.replace(/^(\d+)\. (.*)$/gm, '<li>$2</li>');

    // 将列表项包装在ul或ol中
    let html = markdown;
    // 处理无序列表
    html = html.replace(/(<li>.*?<\/li>)+/gs, function(match) {
      return '<ul>' + match + '</ul>';
    });
    // 处理有序列表
    html = html.replace(/(<li>.*?<\/li>)+/gs, function(match) {
      const lines = match.split('\n');
      let isOrdered = lines[0].match(/^\d+\./);
      if (isOrdered) {
        return '<ol>' + match + '</ol>';
      }
      return match;
    });

    return html;
  }

  // 页面加载后初始化
  window.onload = () => {
    // 渲染导航
    if (typeof categories !== 'undefined') {
      renderCategories('ai');
    } else {
      console.error('Categories data not found');
      window.categories = [];
      renderCategories('ai');
    }

    // 触发滚动动画
    setTimeout(() => {
      window.dispatchEvent(new Event('scroll'));
    }, 100);

    // 添加发送按钮和回车键事件监听
    document.getElementById('sendButton').addEventListener('click', () => {
      sendMessage(document.getElementById('userInput').value);
    });

    document.getElementById('userInput').addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        sendMessage(document.getElementById('userInput').value);
      }
    });
  }
</script>
</body>
</html>
